# QADChat Pro

**基于原项目 [NextChat](https://github.com/ChatGPTNextWeb/NextChat) 的商业化改造版本**

**完整的商业化AI聊天平台，支持前端配置模式和后端管理模式，具备完整的用户管理、服务商管理、统计分析等企业级功能**

## 🚀 核心特性

### 💼 商业化功能
- **双模式运行**：支持前端配置模式和后端管理模式
- **用户认证系统**：完整的用户注册、登录、权限管理
- **现代化管理后台**：Apple风格设计，服务商管理、用户管理、系统配置
- **完整服务商管理**：支持9大主流AI服务商，自定义Base URL，模型选择性启用
- **API中转层**：统一的API代理，完整的调用日志和成本统计
- **安全保障**：API Key加密存储，JWT认证，权限控制

### 🎨 用户体验
- **智能模式切换**：前后端模式无缝切换，自动配置适配
- **友好错误处理**：全面的错误监控和用户提示
- **性能监控**：实时性能监控和优化建议
- **响应式设计**：完美支持桌面端和移动端

## 🚀 快速开始

### 📦 部署方式

#### 源码部署
```bash
# 克隆项目
git clone https://github.com/your-repo/QADChatPro.git
cd QADChatPro

# 安装依赖
yarn install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置数据库等信息

# 初始化数据库
npx prisma migrate dev

# 启动开发服务器
yarn dev

# 或构建生产版本
yarn build
yarn start
```

#### 环境变量配置
```bash
# 数据库配置
DATABASE_URL="sqlite:./data.db"  # 或 MySQL/PostgreSQL 连接字符串

# JWT 密钥
JWT_SECRET="your-jwt-secret-key"

# 管理员账户（可选，首次启动时创建）
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"
ADMIN_EMAIL="<EMAIL>"
```

### 🌐 Demo站点

体验地址：[https://qaduck.com](https://qaduck.com)

### 📖 使用说明

#### 前端配置模式
- 适合个人用户或小团队
- 在设置页面直接配置API密钥
- 所有配置存储在浏览器本地

#### 后端管理模式
- 适合企业用户或多用户场景
- 管理员在后台统一管理服务商配置
- 用户无需配置API密钥，直接使用
- 完整的使用统计和成本分析

### 架构重构

- 将话题与助手(原名为“面具”)绑定，每个助手下的都拥有独立的话题列表，通过切换不同的助手，可以在该助手下快速创建话题。
  即原先整体架构为，话题-消息。该版本重构为助手-话题-消息。
  此重构有助于区分不同助手下的话题，避免所有话题均无条件展示，不便于浏览。同时增强了系统对于助手的依赖程度，能够更有效的利用助手提高效率。

### 用户体验升级

- 全局设置界面重构，清晰的设置分组，快速定位所需配置

![全局设置界面重构](docs/images/readme/settings-ui-refactor.png)

- 模型服务重构与模型管理界面重构

  ![模型服务重构](docs/images/readme/model-service-refactor.png)

  ![模型管理界面](docs/images/readme/model-management-ui.png)

- 一键快速启停 MCP Server

  ![MCP Server 管理](docs/images/readme/mcp-server-management.png)

- 模型选择器 UI 与逻辑重构

  ![模型选择器重构](docs/images/readme/model-selector-refactor.png)

- 助手选择界面重构

  ![助手选择界面重构](docs/images/readme/assistant-selector-refactor.png)
- 可配置的模型能力（视觉、联网、嵌入、工具、思考），通过小图标进行显示，快速浏览模型功能
- 支持模型连通性/可用性测试
- 去除所有原版推广内容
- 始终显示 MCP 功能模块
- 将聊天框上的 action 按钮修改为 ToolTip 的形式
- 固定显示当前使用模型，便于快速切换
- 移除搜索按钮，固定显示“搜索聊天记录”按钮
- 重构模型决策器，逻辑更加清晰，创建新对话时，优先使用当前助手配置的默认模型，若未配置，则使用全局默认模型

### 全新功能模块

- **现代化管理后台**：Apple风格设计的管理界面
  - 服务商管理：支持9大主流AI服务商配置
  - 自定义Base URL：支持代理服务器和自建服务
  - 模型选择性启用：精确控制每个服务商的可用模型
  - 用户权限管理：完整的用户角色和权限控制

- 模型竞技场，选择多个模型进行对话，对比模型之间的差异
  ![模型竞技场](docs/images/readme/multi-model-arena.png)

- 自定义服务商
  ![自定义服务商](docs/images/readme/custom-provider.png)

### MCP更新

- 使用当前主流的 streamableHttp 协议对当前 MCP 通信方式重构，同时兼容 sse 协议。便于后续扩展

- 将内置 MCP 列表修改为代码中嵌入，避免对远程服务的依赖。内置 Context7 和 EdgeOne Pages MCP

### 模块移除

- 移除插件模块，该模块功能与 MCP 高度重复，因此仅保留更为活跃的 MCP 服务
- 移除 SD 绘图模块，当前绘图模型较多，等待后续增加新的绘图界面

## 🎯 商业化功能

### 🔐 用户认证系统
- **用户注册/登录**：完整的用户认证流程
- **JWT认证**：安全的token认证机制
- **角色权限**：用户/管理员角色区分
- **权限控制**：细粒度的功能权限管理

### 🛠️ 管理后台
- **仪表盘**：系统概览、实时统计、活动监控
- **服务商管理**：添加/编辑/删除AI服务商配置
- **用户管理**：用户列表、权限管理、账户操作
- **系统配置**：全局设置、功能开关、参数调整
- **调用日志**：API调用记录、成本统计、性能分析

### 🔄 API中转层
- **统一代理**：所有AI服务商调用统一入口
- **安全隔离**：API Key完全隔离，前端无感知
- **日志记录**：完整的调用日志和错误追踪
- **成本统计**：Token使用量和成本分析
- **性能监控**：响应时间、成功率统计

### 🎨 智能适配
- **双模式运行**：前端配置模式 + 后端管理模式
- **无缝切换**：模式自动检测和界面适配
- **错误处理**：全面的错误监控和用户提示
- **性能优化**：加载优化、缓存策略、响应式设计

## 📋 开发计划

### 已完成功能 ✅
- [x] 用户认证系统
- [x] 管理后台界面
- [x] API中转代理
- [x] 服务商管理
- [x] 调用日志统计
- [x] 双模式适配
- [x] 错误处理优化
- [x] 多模型对话

### 计划功能 🚧
- [ ] Docker容器化部署
- [ ] 多文件类型上传支持
- [ ] 高级统计分析
- [ ] API限流和配额管理
- [ ] 多租户支持
- [ ] 国际化支持
- [ ] 移动端APP

## 📞 技术支持

### 获取帮助
- **Issue反馈**：[GitHub Issues](https://github.com/Syferie/qadchat/issues)
- **功能建议**：通过Issue提交功能需求
- **Bug报告**：详细描述问题和复现步骤
- **文档问题**：文档错误或改进建议

### 商业支持
- **企业部署**：提供企业级部署和定制服务
- **技术咨询**：AI应用架构设计和优化建议
- **培训服务**：系统使用和管理培训

## 📝 更新日志

### v1.2.0 (2025-01-27)
- **🎨 管理面板UI全面重构**：采用现代化Apple风格设计，提升用户体验
- **🔧 导航逻辑优化**：修复侧边栏导航问题，实现同一布局内的页面切换
- **📱 响应式设计改进**：优化移动端和平板端的显示效果
- **🎯 组件架构统一**：创建统一的AdminLayout组件，简化代码维护
- **✨ 交互体验提升**：添加流畅的动画效果和悬停反馈

### v1.1.0 (2025-01-20)
- **🔐 用户认证系统**：完整的用户注册、登录、权限管理
- **⚙️ 服务商管理**：支持9大主流AI服务商配置
- **📊 管理后台**：基础的管理界面和功能
- **🔄 API中转层**：统一的API代理和日志记录

## 🙏 致谢

- **[NextChat](https://github.com/ChatGPTNextWeb/NextChat)** —— 优秀的开源项目基础
- **[Cherry Studio](https://github.com/CherryHQ/cherry-studio)** —— UI设计和功能模块参考
- **开源社区** —— 各种优秀的开源组件和工具

## 📄 许可证

本项目基于 MIT 许可证开源，详见 [LICENSE](LICENSE) 文件。

## 🌟 Star History

如果这个项目对您有帮助，请给我们一个 ⭐️！

[![Star History Chart](https://api.star-history.com/svg?repos=Syferie/qadchat&type=Date)](https://star-history.com/#Syferie/qadchat&Date)