import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkConfig() {
  try {
    console.log('🔍 检查系统配置...');
    
    // 检查系统配置
    const systemConfigs = await prisma.systemConfig.findMany();
    console.log('\n📋 系统配置:');
    systemConfigs.forEach(config => {
      console.log(`  ${config.key}: ${config.value}`);
    });
    
    // 检查服务商配置
    const providers = await prisma.provider.findMany();
    console.log('\n🔧 服务商配置:');
    providers.forEach(provider => {
      console.log(`  ${provider.name} (${provider.type}): enabled=${provider.isEnabled}, models=${provider.config}`);
    });
    
    // 检查用户
    const users = await prisma.user.findMany();
    console.log('\n👥 用户:');
    users.forEach(user => {
      console.log(`  ${user.username}: role=${user.role}`);
    });
    
  } catch (error) {
    console.error('❌ 检查配置时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConfig();
