// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户角色枚举
enum Role {
  USER
  ADMIN
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String   // bcrypt hash
  role      Role     @default(USER)
  enabled   <PERSON>olean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联的API调用日志
  apiLogs   ApiLog[]
  // 关联的用户会话
  sessions  UserSession[]

  @@map("users")
}

// 服务商配置表
model Provider {
  id          String   @id @default(cuid())
  name        String   // 显示名称，如 "OpenAI GPT-4"
  type        String   // 服务商类型：openai, google, anthropic, etc.
  baseUrl     String   // API基础URL
  apiKey      String   // 加密存储的API Key
  enabled     Boolean  @default(true)
  config      String?  // JSON格式的额外配置
  models      String?  // JSON格式的支持模型列表
  description String?  // 描述信息
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联的API调用日志
  apiLogs     ApiLog[]

  @@map("providers")
}

// 系统配置表
model SystemConfig {
  key         String   @id
  value       String   // 配置值
  type        String   // 配置类型：string, number, boolean, json
  description String?  // 配置描述
  category    String?  // 配置分类
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

// API调用日志表
model ApiLog {
  id          String   @id @default(cuid())
  userId      String?  // 用户ID，可为空（匿名用户）
  providerId  String   // 服务商ID
  model       String   // 使用的模型
  endpoint    String   // 调用的端点
  method      String   // HTTP方法
  tokensUsed  Int?     // 使用的token数量
  cost        Float?   // 成本（如果有）
  status      String   // 状态：success, error, timeout
  errorMsg    String?  // 错误信息
  duration    Int?     // 请求耗时（毫秒）
  createdAt   DateTime @default(now())

  // 关联关系
  user        User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  provider    Provider  @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@map("api_logs")
}

// 用户会话表（可选，用于存储聊天会话）
model UserSession {
  id          String   @id @default(cuid())
  userId      String?  // 用户ID，可为空（匿名用户）
  sessionId   String   @unique // 前端会话ID
  title       String?  // 会话标题
  sessionData String   // JSON格式的会话数据
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("user_sessions")
}
