import { PrismaClient, Role } from '../app/generated/prisma';
import { hashPassword } from '../app/lib/encryption';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始数据库种子数据初始化...');

  // 创建默认管理员用户
  const adminUsername = process.env.DEFAULT_ADMIN_USERNAME || 'admin';
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';

  const existingAdmin = await prisma.user.findFirst({
    where: { role: Role.ADMIN }
  });

  if (!existingAdmin) {
    const admin = await prisma.user.create({
      data: {
        username: adminUsername,
        email: adminEmail,
        password: hashPassword(adminPassword),
        role: Role.ADMIN,
        enabled: true,
      },
    });
    console.log(`✅ 创建默认管理员用户: ${admin.username} (${admin.email})`);
  } else {
    console.log('ℹ️  管理员用户已存在，跳过创建');
  }

  // 创建默认系统配置
  const defaultConfigs = [
    {
      key: 'frontendConfigDisabled',
      value: 'false',
      type: 'boolean',
      description: '是否禁用前端配置功能',
      category: 'system',
    },
    {
      key: 'enableBackendConfig',
      value: 'true',
      type: 'boolean',
      description: '是否启用后端配置管理',
      category: 'system',
    },
    {
      key: 'siteName',
      value: 'QADChat',
      type: 'string',
      description: '网站名称',
      category: 'general',
    },
    {
      key: 'siteDescription',
      value: 'AI聊天助手平台',
      type: 'string',
      description: '网站描述',
      category: 'general',
    },
    {
      key: 'allowUserRegistration',
      value: 'true',
      type: 'boolean',
      description: '是否允许用户注册',
      category: 'auth',
    },
    {
      key: 'defaultUserRole',
      value: 'USER',
      type: 'string',
      description: '默认用户角色',
      category: 'auth',
    },
  ];

  for (const config of defaultConfigs) {
    const existing = await prisma.systemConfig.findUnique({
      where: { key: config.key }
    });

    if (!existing) {
      await prisma.systemConfig.create({
        data: config,
      });
      console.log(`✅ 创建系统配置: ${config.key} = ${config.value}`);
    } else {
      console.log(`ℹ️  系统配置 ${config.key} 已存在，跳过创建`);
    }
  }

  console.log('🎉 数据库种子数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('❌ 数据库种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
