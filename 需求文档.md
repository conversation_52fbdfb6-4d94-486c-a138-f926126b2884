## QADChat 商业化后端中转一体化改造需求文档（详细版）

### 1. 项目目标

将 QADChat 由前端直连第三方服务商的模式，升级为前后端一体化、所有敏感信息后端中转、支持多用户和商业化运营的架构。

---

### 2. 当前项目状态分析

#### 2.1. 现有架构

- **前端架构**：Next.js + React + TypeScript + Antd + Zustand 状态管理
- **数据存储**：完全基于浏览器本地存储（localStorage、IndexedDB）
- **API调用**：前端直接调用第三方AI服务商API
- **配置管理**：用户在前端界面配置API Key和服务商信息
- **用户系统**：无用户认证，仅有访问码验证

#### 2.2. 已实现功能

- ✅ 多服务商支持（OpenAI、Google、Anthropic、ByteDance等）
- ✅ 自定义服务商功能
- ✅ 模型管理和配置
- ✅ 聊天会话管理
- ✅ 设置页面和配置界面
- ✅ MCP（Model Context Protocol）集成
- ✅ 基础的API路由结构

#### 2.3. 缺失的商业化功能

- ❌ 数据库集成（Prisma + MySQL/SQLite）
- ❌ 用户认证和权限系统
- ❌ 管理后台界面
- ❌ API中转层（后端代理）
- ❌ 服务商配置的后端管理
- ❌ 用量统计和日志记录
- ❌ 多租户支持

---

### 3. 架构核心

- **技术栈**：Next.js + React + TypeScript + Antd + Zustand + Prisma + MySQL/SQLite
- **部署方式**：Next.js 一体式服务，既负责页面渲染，也负责 API 路由转发
- **安全原则**：API Key、服务商 URL 等敏感信息只在服务器端处理，前端用户不可见
- **数据库策略**：开发环境使用 SQLite，生产环境支持 MySQL，通过环境变量切换

---

### 4. 核心改造点详细分析

#### 4.1. 数据存储架构改造

**当前状态**：

- 所有配置存储在浏览器 localStorage
- 聊天记录存储在 IndexedDB
- 无服务端数据持久化

**需要改造**：

1. **数据库设计**：

   ```sql
   -- 用户表
   users (id, username, email, password_hash, role, created_at, updated_at)
   
   -- 服务商配置表
   providers (id, name, type, base_url, api_key_encrypted, enabled, created_at, updated_at)
   
   -- 系统配置表
   system_config (key, value, type, description, updated_at)
   
   -- 用户会话表
   user_sessions (id, user_id, session_data, created_at, updated_at)
   
   -- API调用日志表
   api_logs (id, user_id, provider_id, model, tokens_used, cost, status, created_at)
   ```

2. **数据迁移策略**：

   - 保持前端配置界面兼容性
   - 根据系统配置决定是否启用后端管理
   - 提供数据导入/导出功能

#### 4.2. API 层中转改造

**当前状态**：

- 前端直接调用第三方API
- API Key在前端暴露
- 无统一的请求管理

**需要改造**：

1. **统一聊天API**：

   ```typescript
   // 新增 /api/chat 统一入口
   POST /api/chat
   {
     "provider": "openai",
     "model": "gpt-4",
     "messages": [...],
     "stream": true,
      //此处还应该包含其他对话参数等，需要确保完整传输，达到和目前的对话系统相同的效果
   }
   ```

2. **服务商路由重构**：

   - 保持现有 `/api/[provider]` 路由兼容
   - 添加后端配置获取逻辑
   - 实现API Key的服务端注入

3. **认证中间件**：

   - JWT token验证
   - 用户权限检查
   - 请求频率限制

#### 4.3. 用户认证系统

**当前状态**：

- 仅有简单的访问码验证
- 无用户概念和权限管理

**需要改造**：

1. **认证API**：

   ```typescript
   POST /api/auth/login
   POST /api/auth/register
   POST /api/auth/logout
   GET /api/auth/me
   ```

2. **前端认证组件**：

   - 改造现有 AuthPage 组件
   - 添加注册/登录切换
   - 集成到主界面的用户入口

3. **权限控制**：

   - 普通用户：使用聊天功能
   - 管理员：访问管理后台

#### 4.4. 管理后台系统

**当前状态**：

- 无管理后台
- 配置分散在设置页面

**需要改造**：

1. **管理后台路由**：

   ```typescript
   /admin/dashboard     // 仪表板
   /admin/providers     // 服务商管理
   /admin/users         // 用户管理
   /admin/settings      // 系统设置
   /admin/logs          // 调用日志
   ```

2. **管理界面组件**：

   - 复用现有聊天界面框架
   - 左侧菜单替换为管理功能
   - 右侧内容区域展示管理表单

3. **管理功能**：

   - 服务商CRUD操作
   - 用户管理和权限设置
   - 系统配置管理
   - 使用统计和日志查看

---

### 5. 详细实施计划

#### 5.1. 第一阶段：数据库和基础架构（优先级：高）

**任务清单**：

1. **Prisma集成**：

   - 安装和配置 Prisma ORM
   - 设计数据库 Schema
   - 创建迁移文件
   - 配置 SQLite（开发）和 MySQL（生产）

2. **数据库表设计**：

   ```prisma
   model User {
     id        String   @id @default(cuid())
     username  String   @unique
     email     String   @unique
     password  String   // bcrypt hash
     role      Role     @default(USER)
     createdAt DateTime @default(now())
     updatedAt DateTime @updatedAt
   }
   
   model Provider {
     id          String   @id @default(cuid())
     name        String
     type        String   // openai, google, anthropic, etc.
     baseUrl     String
     apiKey      String   // encrypted
     enabled     Boolean  @default(true)
     config      Json?    // additional config
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
   }
   
   model SystemConfig {
     key         String   @id
     value       String
     type        String   // string, number, boolean, json
     description String?
     updatedAt   DateTime @updatedAt
   }
   ```

3. **环境配置**：

   - 数据库连接配置
   - 加密密钥设置
   - JWT 密钥配置

#### 5.2. 第二阶段：用户认证系统（优先级：高）

**任务清单**：

1. **认证API开发**：

   ```typescript
   // app/api/auth/login/route.ts
   // app/api/auth/register/route.ts
   // app/api/auth/logout/route.ts
   // app/api/auth/me/route.ts
   ```

2. **前端认证改造**：

   - 改造现有 AuthPage 组件支持注册/登录
   - 添加用户状态管理（Zustand store）
   - 实现 JWT token 存储和自动刷新
   - 改造主界面的用户入口按钮

3. **权限中间件**：

   - 创建认证中间件验证 JWT
   - 实现角色权限检查
   - 保护管理员路由

#### 5.3. 第三阶段：API中转层（优先级：高）

**任务清单**：

1. **统一聊天API**：

   ```typescript
   // app/api/chat/route.ts - 新的统一聊天入口
   // 支持所有服务商的统一接口
   ```

2. **服务商配置API**：

   ```typescript
   // app/api/providers/route.ts - 服务商管理
   // app/api/config/route.ts - 系统配置（改造现有）
   ```

3. **现有API路由改造**：

   - 保持 `/api/[provider]` 兼容性
   - 添加后端配置获取逻辑
   - 实现 API Key 服务端注入

#### 5.4. 第四阶段：管理后台（优先级：中）

**任务清单**：

1. **管理后台路由**：

   ```typescript
   // app/admin/page.tsx - 管理后台主页
   // app/admin/providers/page.tsx - 服务商管理
   // app/admin/users/page.tsx - 用户管理
   // app/admin/settings/page.tsx - 系统设置
   ```

2. **管理界面组件**：

   - 复用聊天界面框架
   - 创建管理菜单组件
   - 实现服务商管理表单
   - 实现用户管理界面

3. **管理功能API**：

   ```typescript
   // app/api/admin/providers/route.ts
   // app/api/admin/users/route.ts
   // app/api/admin/settings/route.ts
   // app/api/admin/logs/route.ts
   ```

#### 5.5. 第五阶段：前端配置逻辑改造（优先级：中）

**任务清单**：

1. **动态配置加载**：
   - 改造 `/api/config` 返回后端配置
   - 前端根据 `frontendConfigDisabled` 控制界面
   - 实现配置的动态切换

2. **设置页面改造**：
   - 保持现有设置页面兼容性
   - 添加后端模式的只读显示
   - 实现平滑的模式切换

#### 5.6. 第六阶段：用量统计和日志（优先级：低）

**任务清单**：

1. **日志记录**：

   ```typescript
   // 在聊天API中添加日志记录
   // 记录用户、模型、token使用量等
   ```

2. **统计API**：

   ```typescript
   // app/api/admin/stats/route.ts
   // 提供使用统计数据
   ```

---

### 6. 关键技术实现点

#### 6.1. 数据存储迁移策略

**问题**：现有数据全部存储在前端，需要平滑迁移到后端

**解决方案**：

1. **双模式运行**：

   - 通过环境变量 `ENABLE_BACKEND_CONFIG` 控制
   - 前端检查系统配置决定使用模式
   - 保持向后兼容

2. **数据迁移工具**：

   ```typescript
   // 提供前端数据导出功能
   // 管理员可以导入用户的配置数据
   ```

#### 6.2. API Key 安全处理

**问题**：API Key 需要从前端迁移到后端，确保安全

**解决方案**：

1. **加密存储**：

   ```typescript
   // 使用 AES 加密存储 API Key
   // 密钥通过环境变量配置
   ```

2. **运行时解密**：

   ```typescript
   // 请求时动态解密 API Key
   // 不在内存中长期保存明文
   ```

#### 6.3. 服务商配置的动态加载

**问题**：现有服务商配置硬编码，需要支持动态配置

**解决方案**：

1. **配置抽象层**：

   ```typescript
   // 创建服务商配置抽象接口
   // 支持从数据库动态加载配置
   ```

2. **热更新机制**：

   ```typescript
   // 配置变更时无需重启服务
   // 实现配置的热重载
   ```

---

### 7. UI/UX 设计细化

#### 7.1. 用户认证界面

- **登录/注册页面**：
  - 复用现有 AuthPage 组件框架
  - 添加注册表单（用户名、邮箱、密码）
  - 实现登录/注册模式切换
  - 保持 QADChat 设计风格

- **用户入口改造**：
  - 改造主界面左下角按钮
  - 登录后显示用户信息和登出选项
  - 管理员显示"管理后台"入口

#### 7.2. 管理后台界面

- **整体布局**：
  - 复用聊天界面的三栏布局
  - 左侧：管理菜单导航
  - 中间：功能内容区域
  - 右侧：可选的详情面板

- **管理菜单**：
  - 仪表盘：系统概览和统计
  - 服务商管理：CRUD 操作，复用现在的全局设置中的“模型服务配置页面”的布局和 UI，以及卡片式设计
  - 用户管理：用户列表和权限
  - 系统设置：全局配置
  - 调用日志：API 使用记录

- **表单设计**：
  - 复用现有设置页面的表单组件
  - 统一的保存/取消操作
  - 实时验证和错误提示

#### 7.3. 配置模式切换

- **兼容性显示**：
  - 后端模式：显示只读的服务商信息
  - 前端模式：保持原有配置界面
  - 平滑的模式切换提示

---

### 8. 预期效果和商业价值

#### 8.1. 技术效果

- **安全性提升**：API Key 完全后端隔离
- **可扩展性**：支持多租户和企业级部署
- **可维护性**：统一的配置管理和监控
- **性能优化**：后端缓存和负载均衡

#### 8.2. 商业价值

- **降低使用门槛**：用户无需配置 API Key
- **运营支持**：用户管理、使用统计、成本控制
- **收入模式**：支持订阅制、按量计费等
- **企业部署**：支持私有化部署和定制化

#### 8.3. 用户体验

- **开箱即用**：注册即可使用，无需复杂配置
- **统一体验**：所有用户使用相同的服务质量
- **管理便捷**：管理员可以统一管理所有配置
- **数据安全**：用户数据和配置的安全保障