import styles from "./auth.module.scss";
import { Icon<PERSON>utton } from "./button";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { useUserStore } from "../store/user";
import Locale from "../locales";
import BotIcon from "../icons/bot.svg";
import LeftIcon from "@/app/icons/left.svg";
import { PasswordInput, Input } from "./ui-lib";
import clsx from "clsx";

type AuthMode = "login" | "register";

export function UserAuthPage() {
  const navigate = useNavigate();
  const userStore = useUserStore();
  const [mode, setMode] = useState<AuthMode>("login");
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const goHome = () => navigate(Path.Home);
  const goChat = () => navigate(Path.Chat);

  // 如果已经登录，直接跳转到聊天页面
  useEffect(() => {
    if (userStore.isAuthenticated) {
      navigate(Path.Chat);
    }
  }, [userStore.isAuthenticated, navigate]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    if (mode === "login") {
      if (!formData.username || !formData.password) {
        userStore.setError("请填写用户名和密码");
        return;
      }

      const success = await userStore.login(
        formData.username,
        formData.password,
      );
      if (success) {
        navigate(Path.Chat);
      }
    } else {
      // 注册模式
      if (!formData.username || !formData.email || !formData.password) {
        userStore.setError("请填写所有必填字段");
        return;
      }

      if (formData.password !== formData.confirmPassword) {
        userStore.setError("两次输入的密码不一致");
        return;
      }

      if (formData.password.length < 6) {
        userStore.setError("密码长度至少6位");
        return;
      }

      const success = await userStore.register(
        formData.username,
        formData.email,
        formData.password,
      );
      if (success) {
        navigate(Path.Chat);
      }
    }
  };

  const switchMode = () => {
    setMode(mode === "login" ? "register" : "login");
    setFormData({
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    userStore.setError(null);
  };

  return (
    <div className={styles["auth-page"]}>
      <div className={styles["auth-header"]}>
        <IconButton
          icon={<LeftIcon />}
          text={Locale.Auth.Return}
          onClick={goHome}
        />
      </div>

      <div className={clsx("no-dark", styles["auth-logo"])}>
        <BotIcon />
      </div>

      <div className={styles["auth-title"]}>
        {mode === "login" ? "登录 QADChat" : "注册 QADChat"}
      </div>

      <div className={styles["auth-tips"]}>
        {mode === "login" ? "请输入您的用户名和密码" : "创建您的 QADChat 账户"}
      </div>

      {/* 错误信息显示 */}
      {userStore.error && (
        <div
          style={{
            color: "red",
            marginBottom: "1rem",
            textAlign: "center",
            fontSize: "14px",
          }}
        >
          {userStore.error}
        </div>
      )}

      {/* 用户名输入 */}
      <Input
        style={{ marginTop: "2vh", marginBottom: "1vh" }}
        aria-label="用户名"
        value={formData.username}
        type="text"
        placeholder="用户名"
        onChange={(e) => handleInputChange("username", e.currentTarget.value)}
      />

      {/* 邮箱输入（仅注册时显示） */}
      {mode === "register" && (
        <Input
          style={{ marginBottom: "1vh" }}
          aria-label="邮箱"
          value={formData.email}
          type="email"
          placeholder="邮箱地址"
          onChange={(e) => handleInputChange("email", e.currentTarget.value)}
        />
      )}

      {/* 密码输入 */}
      <PasswordInput
        style={{ marginBottom: "1vh" }}
        aria-label="密码"
        value={formData.password}
        type="password"
        placeholder="密码"
        onChange={(e) => handleInputChange("password", e.currentTarget.value)}
      />

      {/* 确认密码输入（仅注册时显示） */}
      {mode === "register" && (
        <PasswordInput
          style={{ marginBottom: "2vh" }}
          aria-label="确认密码"
          value={formData.confirmPassword}
          type="password"
          placeholder="确认密码"
          onChange={(e) =>
            handleInputChange("confirmPassword", e.currentTarget.value)
          }
        />
      )}

      <div className={styles["auth-actions"]}>
        <IconButton
          text={mode === "login" ? "登录" : "注册"}
          type="primary"
          onClick={handleSubmit}
          disabled={userStore.isLoading}
        />

        <IconButton
          text={mode === "login" ? "没有账户？点击注册" : "已有账户？点击登录"}
          onClick={switchMode}
          disabled={userStore.isLoading}
        />
      </div>
    </div>
  );
}
