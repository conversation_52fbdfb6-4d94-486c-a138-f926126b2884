.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  gap: 16px;
  min-width: 280px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  svg {
    width: 30px;
    height: 30px;
  }
}

.user-details {
  text-align: center;
  
  .user-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 4px;
  }
  
  .user-email {
    font-size: 14px;
    color: var(--gray);
    margin-bottom: 4px;
  }
  
  .user-role {
    font-size: 12px;
    color: var(--primary);
    background: var(--primary-alpha);
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
  }
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  
  .admin-button {
    background: var(--primary);
    color: white;
    
    &:hover {
      background: var(--primary-hover);
    }
  }
  
  .logout-button {
    background: var(--red);
    color: white;
    
    &:hover {
      background: var(--red-hover);
    }
  }
}
