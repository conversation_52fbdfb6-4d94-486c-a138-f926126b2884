import { useState, useEffect } from "react";
import { Card } from "../ui-lib";
import styles from "./admin-dashboard.module.scss";

interface DashboardStats {
  totalUsers: number;
  totalProviders: number;
  totalApiCalls: number;
  successRate: string;
  totalTokens: number;
  totalCost: number;
}

interface RecentActivity {
  id: string;
  type: "api_call" | "user_register" | "provider_add";
  message: string;
  time: string;
  status: "success" | "error" | "info";
}

export function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalProviders: 0,
    totalApiCalls: 0,
    successRate: "0%",
    totalTokens: 0,
    totalCost: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 调用真实的API获取统计数据
      const response = await fetch("/api/admin/stats?days=30");
      const data = await response.json();

      if (data.success) {
        const { overview } = data.stats;
        setStats({
          totalUsers: overview.totalUsers,
          totalProviders: overview.totalProviders,
          totalApiCalls: overview.totalCalls,
          successRate: overview.successRate + "%",
          totalTokens: overview.totalTokens,
          totalCost: overview.totalCost,
        });

        // 生成最近活动（基于真实数据的模拟）
        const activities: RecentActivity[] = [];

        if (overview.recentUsers > 0) {
          activities.push({
            id: "recent-users",
            type: "user_register",
            message: `最近30天新增 ${overview.recentUsers} 个用户`,
            time: "今天",
            status: "info",
          });
        }

        if (overview.totalCalls > 0) {
          activities.push({
            id: "api-calls",
            type: "api_call",
            message: `今日API调用 ${Math.floor(overview.totalCalls / 30)} 次`,
            time: "今天",
            status: "success",
          });
        }

        if (data.stats.topProviders.length > 0) {
          const topProvider = data.stats.topProviders[0];
          activities.push({
            id: "top-provider",
            type: "provider_add",
            message: `${topProvider.provider.name} 是最活跃的服务商`,
            time: "统计数据",
            status: "info",
          });
        }

        if (parseFloat(overview.successRate) < 95) {
          activities.push({
            id: "low-success",
            type: "api_call",
            message: `API成功率较低: ${overview.successRate}%`,
            time: "需要关注",
            status: "error",
          });
        }

        setRecentActivity(activities);

        console.log("[Dashboard] Loaded real stats:", overview);
      } else {
        console.error("Failed to load stats:", data.error);
        // 使用默认数据
        setStats({
          totalUsers: 0,
          totalProviders: 0,
          totalApiCalls: 0,
          successRate: "0%",
          totalTokens: 0,
          totalCost: 0,
        });
        setRecentActivity([]);
      }
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      // 使用默认数据
      setStats({
        totalUsers: 0,
        totalProviders: 0,
        totalApiCalls: 0,
        successRate: "0%",
        totalTokens: 0,
        totalCost: 0,
      });
      setRecentActivity([]);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: RecentActivity["type"]) => {
    switch (type) {
      case "api_call":
        return "🔄";
      case "user_register":
        return "👤";
      case "provider_add":
        return "⚙️";
      default:
        return "📝";
    }
  };

  const getActivityColor = (status: RecentActivity["status"]) => {
    switch (status) {
      case "success":
        return "#52c41a";
      case "error":
        return "#ff4d4f";
      case "info":
        return "#1890ff";
      default:
        return "#666";
    }
  };

  if (loading) {
    return (
      <div className={styles["dashboard-loading"]}>
        <div>正在加载仪表盘数据...</div>
      </div>
    );
  }

  return (
    <div className={styles["admin-dashboard"]}>
      {/* 页面标题 */}
      <div className={styles["dashboard-header"]}>
        <h1>仪表盘</h1>
        <p>系统概览和实时统计</p>
      </div>

      {/* 统计卡片 */}
      <div className={styles["stats-grid"]}>
        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>👥</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.totalUsers}</div>
            <div className={styles["stat-label"]}>总用户数</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>⚙️</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.totalProviders}</div>
            <div className={styles["stat-label"]}>服务商数量</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>📊</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              {stats.totalApiCalls.toLocaleString()}
            </div>
            <div className={styles["stat-label"]}>API调用次数</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>✅</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.successRate}</div>
            <div className={styles["stat-label"]}>成功率</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>🎯</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              {stats.totalTokens.toLocaleString()}
            </div>
            <div className={styles["stat-label"]}>Token使用量</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>💰</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              ${stats.totalCost.toFixed(2)}
            </div>
            <div className={styles["stat-label"]}>总成本</div>
          </div>
        </Card>
      </div>

      {/* 最近活动 */}
      <div className={styles["recent-activity"]}>
        <Card>
          <div className={styles["activity-header"]}>
            <h3>最近活动</h3>
            <button
              onClick={loadDashboardData}
              className={styles["refresh-button"]}
            >
              刷新
            </button>
          </div>
          <div className={styles["activity-list"]}>
            {recentActivity.map((activity) => (
              <div key={activity.id} className={styles["activity-item"]}>
                <div className={styles["activity-icon"]}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className={styles["activity-content"]}>
                  <div className={styles["activity-message"]}>
                    {activity.message}
                  </div>
                  <div className={styles["activity-time"]}>{activity.time}</div>
                </div>
                <div
                  className={styles["activity-status"]}
                  style={{ color: getActivityColor(activity.status) }}
                >
                  ●
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
