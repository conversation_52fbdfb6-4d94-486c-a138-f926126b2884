import { useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../../store/user";
import { IconButton } from "../button";
import { Modal, showConfirm } from "../ui-lib";
import styles from "./admin-layout.module.scss";

// 图标导入
import DashboardIcon from "../../icons/dashboard.svg";
import ProvidersIcon from "../../icons/settings.svg";
import UsersIcon from "../../icons/user.svg";
import SettingsIcon from "../../icons/settings.svg";
import LogsIcon from "../../icons/log.svg";
import BackIcon from "../../icons/left.svg";
import LogoutIcon from "../../icons/logout.svg";

interface AdminLayoutProps {
  children: ReactNode;
}

interface MenuItem {
  id: string;
  name: string;
  icon: ReactNode;
  path: string;
}

const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    name: "仪表盘",
    icon: <DashboardIcon />,
    path: "/admin",
  },
  {
    id: "providers",
    name: "服务商管理",
    icon: <ProvidersIcon />,
    path: "/admin/providers",
  },
  {
    id: "users",
    name: "用户管理",
    icon: <UsersIcon />,
    path: "/admin/users",
  },
  {
    id: "settings",
    name: "系统设置",
    icon: <SettingsIcon />,
    path: "/admin/settings",
  },
  {
    id: "logs",
    name: "调用日志",
    icon: <LogsIcon />,
    path: "/admin/logs",
  },
];

export function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const userStore = useUserStore();
  const [activeMenu, setActiveMenu] = useState("dashboard");

  const handleMenuClick = (item: MenuItem) => {
    setActiveMenu(item.id);
    router.push(item.path);
  };

  const handleBackToChat = () => {
    router.push("/");
  };

  const handleLogout = () => {
    showConfirm({
      title: "确认登出",
      content: "您确定要登出管理后台吗？",
      onConfirm: async () => {
        await userStore.logout();
        router.push("/");
      },
    });
  };

  return (
    <div className={styles["admin-layout"]}>
      {/* 左侧菜单栏 */}
      <div className={styles["admin-sidebar"]}>
        {/* 头部 */}
        <div className={styles["admin-header"]}>
          <div className={styles["admin-title"]}>QADChat 管理后台</div>
          <div className={styles["admin-user"]}>{userStore.user?.username}</div>
        </div>

        {/* 菜单列表 */}
        <div className={styles["admin-menu"]}>
          {menuItems.map((item) => (
            <div
              key={item.id}
              className={`${styles["menu-item"]} ${
                activeMenu === item.id ? styles["active"] : ""
              }`}
              onClick={() => handleMenuClick(item)}
            >
              <div className={styles["menu-icon"]}>{item.icon}</div>
              <div className={styles["menu-name"]}>{item.name}</div>
            </div>
          ))}
        </div>

        {/* 底部操作 */}
        <div className={styles["admin-footer"]}>
          <IconButton
            icon={<BackIcon />}
            text="返回聊天"
            onClick={handleBackToChat}
            className={styles["footer-button"]}
          />
          <IconButton
            icon={<LogoutIcon />}
            text="登出"
            onClick={handleLogout}
            className={styles["footer-button"]}
          />
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className={styles["admin-content"]}>{children}</div>
    </div>
  );
}
