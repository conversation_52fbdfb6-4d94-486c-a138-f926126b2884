import { useState, ReactNode, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useUserStore } from "../../store/user";
import { IconButton } from "../button";
import { Modal, showConfirm } from "../ui-lib";
import styles from "./admin-layout.module.scss";

// 图标导入
import DashboardIcon from "../../icons/dashboard.svg";
import ProvidersIcon from "../../icons/settings.svg";
import UsersIcon from "../../icons/user.svg";
import SettingsIcon from "../../icons/settings.svg";
import LogsIcon from "../../icons/log.svg";
import BackIcon from "../../icons/left.svg";
import LogoutIcon from "../../icons/logout.svg";

interface AdminLayoutProps {
  children: ReactNode;
}

interface MenuItem {
  id: string;
  name: string;
  icon: ReactNode;
  path: string;
}

const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    name: "仪表盘",
    icon: <DashboardIcon />,
    path: "/admin",
  },
  {
    id: "providers",
    name: "服务商管理",
    icon: <ProvidersIcon />,
    path: "/admin/providers",
  },
  {
    id: "users",
    name: "用户管理",
    icon: <UsersIcon />,
    path: "/admin/users",
  },
  {
    id: "settings",
    name: "系统设置",
    icon: <SettingsIcon />,
    path: "/admin/settings",
  },
  {
    id: "logs",
    name: "调用日志",
    icon: <LogsIcon />,
    path: "/admin/logs",
  },
];

export function AdminLayout({ children }: AdminLayoutProps) {
  console.log("[AdminLayout] Component initializing...");

  const router = useRouter();
  const pathname = usePathname();
  const [activeMenu, setActiveMenu] = useState("dashboard");
  const [isClient, setIsClient] = useState(false);

  // 确保在客户端才调用store
  useEffect(() => {
    setIsClient(true);
  }, []);

  console.log("[AdminLayout] About to call useUserStore...");
  const userStore = isClient ? useUserStore() : null;
  console.log("[AdminLayout] useUserStore called successfully:", !!userStore);

  // 根据当前路径设置活动菜单
  useEffect(() => {
    const currentItem = menuItems.find(item => item.path === pathname);
    if (currentItem) {
      setActiveMenu(currentItem.id);
    }
  }, [pathname]);

  const handleMenuClick = (item: MenuItem) => {
    setActiveMenu(item.id);
    router.push(item.path);
  };

  const handleBackToChat = () => {
    router.push("/");
  };

  const handleLogout = () => {
    if (!userStore) return;

    showConfirm({
      title: "确认登出",
      content: "您确定要登出管理后台吗？",
      onConfirm: async () => {
        await userStore.logout();
        router.push("/");
      },
    });
  };

  // 如果还在服务端或store未初始化，显示加载状态
  if (!isClient || !userStore) {
    return (
      <div style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        background: "#f8f9fa",
        color: "#86868b",
        fontSize: "17px",
        fontWeight: "500"
      }}>
        正在初始化管理后台...
      </div>
    );
  }

  return (
    <div className={styles["admin-layout"]}>
      {/* 左侧菜单栏 */}
      <div className={styles["admin-sidebar"]}>
        {/* 头部 */}
        <div className={styles["admin-header"]}>
          <div className={styles["admin-title"]}>QADChat 管理后台</div>
          <div className={styles["admin-user"]}>{userStore?.user?.username || "管理员"}</div>
        </div>

        {/* 菜单列表 */}
        <div className={styles["admin-menu"]}>
          {menuItems.map((item) => (
            <div
              key={item.id}
              className={`${styles["menu-item"]} ${
                activeMenu === item.id ? styles["active"] : ""
              }`}
              onClick={() => handleMenuClick(item)}
            >
              <div className={styles["menu-icon"]}>{item.icon}</div>
              <div className={styles["menu-name"]}>{item.name}</div>
            </div>
          ))}
        </div>

        {/* 底部操作 */}
        <div className={styles["admin-footer"]}>
          <button
            className={styles["footer-button"]}
            onClick={handleBackToChat}
          >
            <BackIcon />
            返回聊天
          </button>
          <button
            className={`${styles["footer-button"]} ${styles["logout"]}`}
            onClick={handleLogout}
          >
            <LogoutIcon />
            登出
          </button>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className={styles["admin-content"]}>{children}</div>
    </div>
  );
}
