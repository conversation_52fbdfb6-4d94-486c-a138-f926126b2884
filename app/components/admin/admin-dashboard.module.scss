// Apple风格的仪表盘样式
.admin-dashboard {
  padding: 32px;
  height: 100%;
  overflow-y: auto;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 17px;
  color: #86868b;
  font-weight: 500;
}

.dashboard-header {
  margin-bottom: 32px;

  h1 {
    font-size: 34px;
    font-weight: 700;
    color: #1d1d1f;
    margin: 0 0 8px 0;
    letter-spacing: -0.022em;
  }

  p {
    font-size: 17px;
    color: #86868b;
    margin: 0;
    font-weight: 400;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e5e7;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-4px);
    border-color: #d1d1d6;
  }
}

.stat-icon {
  font-size: 28px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  border-radius: 16px;
  color: #007aff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  margin-bottom: 4px;
  letter-spacing: -0.022em;
}

.stat-label {
  font-size: 15px;
  color: #86868b;
  font-weight: 500;
}

.recent-activity {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e5e7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 24px 24px 20px 24px;
    border-bottom: 1px solid #e5e5e7;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #1d1d1f;
      margin: 0;
      letter-spacing: -0.022em;
    }

    .refresh-button {
      background: #007aff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: #0056cc;
        transform: translateY(-1px);
      }
    }
  }
}

.activity-list {
  padding: 0 24px 24px 24px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f7;
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8f9fa;
    margin: 0 -16px;
    padding: 16px;
    border-radius: 12px;
  }
}

.activity-icon {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  border-radius: 12px;
  color: #007aff;
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: 15px;
  color: #1d1d1f;
  margin-bottom: 4px;
  font-weight: 500;
  line-height: 1.4;
}

.activity-time {
  font-size: 13px;
  color: #86868b;
  font-weight: 400;
}

.activity-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 6px;

  &.success {
    color: #30d158;
    background: #e8f5e8;
  }

  &.error {
    color: #ff3b30;
    background: #ffebea;
  }

  &.info {
    color: #007aff;
    background: #e8f4ff;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .admin-dashboard {
    padding: 24px;
  }

  .dashboard-header {
    margin-bottom: 24px;

    h1 {
      font-size: 30px;
    }

    p {
      font-size: 16px;
    }
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: 20px;
  }

  .dashboard-header {
    margin-bottom: 20px;

    h1 {
      font-size: 26px;
    }

    p {
      font-size: 15px;
    }
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
  }

  .stat-card {
    padding: 20px;
    gap: 16px;
  }

  .stat-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 14px;
  }

  .recent-activity {
    .activity-header {
      padding: 20px 20px 16px 20px;

      h3 {
        font-size: 18px;
      }

      .refresh-button {
        padding: 6px 12px;
        font-size: 13px;
      }
    }
  }

  .activity-list {
    padding: 0 20px 20px 20px;
  }

  .activity-item {
    gap: 12px;
    padding: 12px 0;

    &:hover {
      margin: 0 -12px;
      padding: 12px;
    }
  }

  .activity-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .activity-message {
    font-size: 14px;
  }

  .activity-time {
    font-size: 12px;
  }
}
