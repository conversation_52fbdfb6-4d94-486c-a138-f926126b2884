.admin-dashboard {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: var(--gray-50);
}

.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: var(--gray);
}

.dashboard-header {
  margin-bottom: 24px;
  
  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: var(--gray);
    margin: 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.stat-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-alpha);
  border-radius: 12px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--gray);
}

.recent-activity {
  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 20px 16px 20px;
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--black);
      margin: 0;
    }
    
    .refresh-button {
      background: var(--primary);
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      transition: background 0.2s ease;
      
      &:hover {
        background: var(--primary-hover);
      }
    }
  }
}

.activity-list {
  padding: 0 20px 20px 20px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  border-radius: 8px;
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: 14px;
  color: var(--black);
  margin-bottom: 2px;
}

.activity-time {
  font-size: 12px;
  color: var(--gray);
}

.activity-status {
  font-size: 12px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }
  
  .dashboard-header {
    h1 {
      font-size: 24px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 12px;
  }
}
