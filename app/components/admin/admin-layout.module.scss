// Apple风格的现代化管理面板样式
.admin-layout {
  display: flex;
  height: 100vh;
  background: #f8f9fa;
  color: #1d1d1f;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.admin-sidebar {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e5e5e7;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04);
}

.admin-header {
  padding: 32px 24px 24px 24px;
  border-bottom: 1px solid #e5e5e7;

  .admin-title {
    font-size: 22px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
    letter-spacing: -0.022em;
  }

  .admin-user {
    font-size: 14px;
    color: #86868b;
    background: #f5f5f7;
    padding: 6px 12px;
    border-radius: 20px;
    display: inline-block;
    font-weight: 500;
  }
}

.admin-menu {
  flex: 1;
  padding: 24px 16px;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 12px;
  margin-bottom: 4px;
  position: relative;

  &:hover {
    background: #f5f5f7;
    transform: translateY(-1px);
  }

  &.active {
    background: #007aff;
    color: white;
    box-shadow: 0 4px 14px rgba(0, 122, 255, 0.3);

    .menu-icon svg {
      color: white;
    }

    &:hover {
      background: #0056cc;
      transform: translateY(-1px);
    }
  }

  .menu-icon {
    width: 24px;
    height: 24px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 20px;
      height: 20px;
      color: #86868b;
      transition: color 0.2s ease;
    }
  }

  .menu-name {
    font-size: 15px;
    font-weight: 500;
    letter-spacing: -0.01em;
  }
}

.admin-footer {
  padding: 24px 16px;
  border-top: 1px solid #e5e5e7;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .footer-button {
    width: 100%;
    justify-content: flex-start;
    padding: 12px 16px;
    font-size: 15px;
    font-weight: 500;
    border: none;
    background: transparent;
    color: #1d1d1f;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    gap: 12px;

    &:hover {
      background: #f5f5f7;
      transform: translateY(-1px);
    }

    &.logout {
      color: #ff3b30;

      &:hover {
        background: #ffebea;
      }
    }
  }
}

.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 240px;
  }

  .admin-header {
    padding: 24px 20px 20px 20px;

    .admin-title {
      font-size: 20px;
    }
  }

  .admin-menu {
    padding: 20px 12px;
  }

  .menu-item {
    padding: 12px 14px;

    .menu-icon {
      width: 22px;
      height: 22px;
      margin-right: 14px;

      svg {
        width: 18px;
        height: 18px;
      }
    }

    .menu-name {
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e5e5e7;
  }

  .admin-header {
    padding: 20px 16px 16px 16px;

    .admin-title {
      font-size: 18px;
    }

    .admin-user {
      font-size: 13px;
      padding: 4px 10px;
    }
  }

  .admin-menu {
    display: flex;
    overflow-x: auto;
    padding: 12px 16px;
    gap: 8px;

    .menu-item {
      flex-shrink: 0;
      margin: 0;
      min-width: 100px;
      justify-content: center;
      padding: 10px 12px;

      .menu-icon {
        margin-right: 8px;
        width: 20px;
        height: 20px;

        svg {
          width: 16px;
          height: 16px;
        }
      }

      .menu-name {
        font-size: 13px;
      }
    }
  }

  .admin-footer {
    padding: 16px;
    flex-direction: row;
    gap: 12px;

    .footer-button {
      flex: 1;
      padding: 10px 14px;
      font-size: 14px;
      justify-content: center;
    }
  }
}
