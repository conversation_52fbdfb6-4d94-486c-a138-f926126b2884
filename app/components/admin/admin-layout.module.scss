.admin-layout {
  display: flex;
  height: 100vh;
  background: var(--white);
  color: var(--black);
}

.admin-sidebar {
  width: 260px;
  background: var(--gray-50);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.admin-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  
  .admin-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 8px;
  }
  
  .admin-user {
    font-size: 14px;
    color: var(--gray);
    background: var(--primary-alpha);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
  }
}

.admin-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 0 10px;
  border-radius: 8px;
  
  &:hover {
    background: var(--hover-color);
  }
  
  &.active {
    background: var(--primary);
    color: white;
    
    .menu-icon svg {
      color: white;
    }
  }
  
  .menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    svg {
      width: 18px;
      height: 18px;
      color: var(--gray);
    }
  }
  
  .menu-name {
    font-size: 14px;
    font-weight: 500;
  }
}

.admin-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .footer-button {
    width: 100%;
    justify-content: flex-start;
    padding: 8px 12px;
    font-size: 14px;
    
    &:hover {
      background: var(--hover-color);
    }
  }
}

.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--white);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .admin-menu {
    display: flex;
    overflow-x: auto;
    padding: 10px;
    
    .menu-item {
      flex-shrink: 0;
      margin: 0 5px;
      min-width: 120px;
      justify-content: center;
      
      .menu-name {
        display: none;
      }
    }
  }
  
  .admin-footer {
    display: none;
  }
}
