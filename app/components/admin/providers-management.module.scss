// Apple风格的服务商管理样式
.providers-management {
  padding: 32px;
  height: 100%;
  overflow-y: auto;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 17px;
  color: #86868b;
  font-weight: 500;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;

  h1 {
    font-size: 34px;
    font-weight: 700;
    color: #1d1d1f;
    margin: 0 0 8px 0;
    letter-spacing: -0.022em;
  }

  p {
    font-size: 17px;
    color: #86868b;
    margin: 0;
    font-weight: 400;
  }
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
  gap: 24px;
}

.provider-card {
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e5e7;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-4px);
    border-color: #d1d1d6;
  }
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 16px;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
    margin: 0;
    letter-spacing: -0.022em;
  }
}

.provider-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  border-radius: 12px;
  font-size: 24px;
}

.provider-type {
  background: #e8f4ff;
  color: #007aff;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.provider-description {
  font-size: 15px;
  color: #86868b;
  margin-bottom: 20px;
  line-height: 1.4;
}

.provider-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;

  .status-badge {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;

    &.enabled {
      background: #e8f5e8;
      color: #30d158;
    }

    &.disabled {
      background: #ffebea;
      color: #ff3b30;
    }
  }

  .api-key-status {
    font-size: 13px;
    color: #86868b;
  }
}

.provider-status {
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    &.enabled {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }
    
    &.disabled {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }
  }
}

.provider-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: var(--gray);
  min-width: 80px;
  margin-right: 8px;
}

.detail-value {
  font-size: 14px;
  color: var(--black);
  flex: 1;
  word-break: break-all;
}

.provider-details {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: #86868b;
  font-weight: 500;
  min-width: 80px;
  margin-right: 12px;
}

.detail-value {
  font-size: 14px;
  color: #1d1d1f;
  font-weight: 400;
  word-break: break-all;
}

.provider-actions {
  display: flex;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e5e7;

  .delete-button {
    color: #ff3b30;

    &:hover {
      background: #ffebea;
    }
  }
}

.provider-form {
  .form-row {
    margin-bottom: 20px;

    label {
      display: block;
      font-size: 15px;
      font-weight: 500;
      color: #1d1d1f;
      margin-bottom: 8px;
      letter-spacing: -0.01em;
    }

    input[type="checkbox"] {
      margin-right: 12px;
      width: 16px;
      height: 16px;
      accent-color: #007aff;
    }
  }

  .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e5e5e7;
    border-radius: 10px;
    font-size: 15px;
    background: #ffffff;
    color: #1d1d1f;
    font-family: inherit;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #007aff;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e5e5e7;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .providers-management {
    padding: 24px;
  }

  .providers-grid {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
  }

  .page-header {
    margin-bottom: 24px;

    h1 {
      font-size: 30px;
    }

    p {
      font-size: 16px;
    }
  }
}

@media (max-width: 768px) {
  .providers-management {
    padding: 20px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    margin-bottom: 20px;

    h1 {
      font-size: 26px;
    }

    p {
      font-size: 15px;
    }
  }

  .providers-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .provider-card {
    padding: 20px;
  }

  .provider-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .provider-info {
    gap: 12px;

    h3 {
      font-size: 18px;
    }
  }

  .provider-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .provider-actions {
    flex-direction: column;
    gap: 8px;
  }

  .detail-label {
    min-width: 70px;
    font-size: 13px;
  }

  .detail-value {
    font-size: 13px;
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
}
