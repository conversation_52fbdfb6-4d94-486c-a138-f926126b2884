.providers-management {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: var(--gray-50);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: var(--gray);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: var(--gray);
    margin: 0;
  }
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.provider-card {
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.provider-info {
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 4px 0;
  }
}

.provider-type {
  background: var(--primary-alpha);
  color: var(--primary);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.provider-status {
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    &.enabled {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }
    
    &.disabled {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }
  }
}

.provider-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: var(--gray);
  min-width: 80px;
  margin-right: 8px;
}

.detail-value {
  font-size: 14px;
  color: var(--black);
  flex: 1;
  word-break: break-all;
}

.provider-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  
  .delete-button {
    color: var(--red);
    
    &:hover {
      background: var(--red-alpha);
    }
  }
}

.provider-form {
  .form-row {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--black);
      margin-bottom: 8px;
    }
    
    input[type="checkbox"] {
      margin-right: 8px;
    }
  }
  
  .form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: var(--black);
    
    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 2px var(--primary-alpha);
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .providers-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .providers-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .provider-card {
    padding: 16px;
  }
  
  .provider-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .provider-actions {
    flex-direction: column;
  }
}
