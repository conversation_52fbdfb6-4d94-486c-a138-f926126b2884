import { useState, useEffect } from "react";
import { Card, Modal, showConfirm } from "../ui-lib";
import { IconButton } from "../button";
import { Input, PasswordInput } from "../ui-lib";
import styles from "./providers-management.module.scss";

// 图标
import AddIcon from "../../icons/add.svg";
import EditIcon from "../../icons/edit.svg";
import DeleteIcon from "../../icons/delete.svg";
import EyeIcon from "../../icons/eye.svg";

interface Provider {
  id: string;
  name: string;
  type: string;
  baseUrl: string;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProviderFormData {
  name: string;
  type: string;
  baseUrl: string;
  apiKey: string;
  enabled: boolean;
  description: string;
}

export function ProvidersManagement() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [formData, setFormData] = useState<ProviderFormData>({
    name: "",
    type: "openai",
    baseUrl: "",
    apiKey: "",
    enabled: true,
    description: "",
  });

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/providers");
      const data = await response.json();

      if (data.success) {
        setProviders(data.providers);
      } else {
        console.error("Failed to load providers:", data.error);
      }
    } catch (error) {
      console.error("Failed to load providers:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingProvider(null);
    setFormData({
      name: "",
      type: "openai",
      baseUrl: "",
      apiKey: "",
      enabled: true,
      description: "",
    });
    setShowModal(true);
  };

  const handleEdit = async (provider: Provider) => {
    try {
      // 获取完整的服务商信息（包括API Key掩码）
      const response = await fetch(`/api/providers/${provider.id}`);
      const data = await response.json();

      if (data.success) {
        setEditingProvider(provider);
        setFormData({
          name: data.provider.name,
          type: data.provider.type,
          baseUrl: data.provider.baseUrl,
          apiKey: "", // 编辑时不显示API Key
          enabled: data.provider.enabled,
          description: data.provider.description || "",
        });
        setShowModal(true);
      }
    } catch (error) {
      console.error("Failed to load provider details:", error);
    }
  };

  const handleDelete = (provider: Provider) => {
    showConfirm({
      title: "确认删除",
      content: `您确定要删除服务商 "${provider.name}" 吗？此操作不可撤销。`,
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/providers/${provider.id}`, {
            method: "DELETE",
          });

          if (response.ok) {
            await loadProviders();
          } else {
            console.error("Failed to delete provider");
          }
        } catch (error) {
          console.error("Failed to delete provider:", error);
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const url = editingProvider
        ? `/api/providers/${editingProvider.id}`
        : "/api/providers";

      const method = editingProvider ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setShowModal(false);
        await loadProviders();
      } else {
        const data = await response.json();
        console.error("Failed to save provider:", data.error);
      }
    } catch (error) {
      console.error("Failed to save provider:", error);
    }
  };

  const getProviderTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      openai: "OpenAI",
      google: "Google",
      anthropic: "Anthropic",
      bytedance: "ByteDance",
      alibaba: "Alibaba",
      moonshot: "Moonshot",
      xai: "XAI",
      deepseek: "DeepSeek",
      siliconflow: "SiliconFlow",
    };
    return typeMap[type] || type;
  };

  if (loading) {
    return (
      <div className={styles["loading"]}>
        <div>正在加载服务商列表...</div>
      </div>
    );
  }

  return (
    <div className={styles["providers-management"]}>
      {/* 页面标题 */}
      <div className={styles["page-header"]}>
        <div>
          <h1>服务商管理</h1>
          <p>管理AI服务商配置和API密钥</p>
        </div>
        <IconButton
          icon={<AddIcon />}
          text="添加服务商"
          type="primary"
          onClick={handleAdd}
        />
      </div>

      {/* 服务商列表 */}
      <div className={styles["providers-grid"]}>
        {providers.map((provider) => (
          <Card key={provider.id} className={styles["provider-card"]}>
            <div className={styles["provider-header"]}>
              <div className={styles["provider-info"]}>
                <h3>{provider.name}</h3>
                <span className={styles["provider-type"]}>
                  {getProviderTypeLabel(provider.type)}
                </span>
              </div>
              <div className={styles["provider-status"]}>
                <span
                  className={`${styles["status-badge"]} ${
                    provider.enabled ? styles["enabled"] : styles["disabled"]
                  }`}
                >
                  {provider.enabled ? "启用" : "禁用"}
                </span>
              </div>
            </div>

            <div className={styles["provider-details"]}>
              <div className={styles["detail-item"]}>
                <span className={styles["detail-label"]}>API地址:</span>
                <span className={styles["detail-value"]}>
                  {provider.baseUrl}
                </span>
              </div>
              {provider.description && (
                <div className={styles["detail-item"]}>
                  <span className={styles["detail-label"]}>描述:</span>
                  <span className={styles["detail-value"]}>
                    {provider.description}
                  </span>
                </div>
              )}
              <div className={styles["detail-item"]}>
                <span className={styles["detail-label"]}>创建时间:</span>
                <span className={styles["detail-value"]}>
                  {new Date(provider.createdAt).toLocaleString()}
                </span>
              </div>
            </div>

            <div className={styles["provider-actions"]}>
              <IconButton
                icon={<EditIcon />}
                text="编辑"
                onClick={() => handleEdit(provider)}
              />
              <IconButton
                icon={<DeleteIcon />}
                text="删除"
                onClick={() => handleDelete(provider)}
                className={styles["delete-button"]}
              />
            </div>
          </Card>
        ))}
      </div>

      {/* 添加/编辑模态框 */}
      {showModal && (
        <Modal
          title={editingProvider ? "编辑服务商" : "添加服务商"}
          onClose={() => setShowModal(false)}
        >
          <div className={styles["provider-form"]}>
            <div className={styles["form-row"]}>
              <label>服务商名称</label>
              <Input
                value={formData.name}
                placeholder="例如：OpenAI GPT-4"
                onChange={(e) =>
                  setFormData({ ...formData, name: e.currentTarget.value })
                }
              />
            </div>

            <div className={styles["form-row"]}>
              <label>服务商类型</label>
              <select
                value={formData.type}
                onChange={(e) =>
                  setFormData({ ...formData, type: e.currentTarget.value })
                }
                className={styles["form-select"]}
              >
                <option value="openai">OpenAI</option>
                <option value="google">Google</option>
                <option value="anthropic">Anthropic</option>
                <option value="bytedance">ByteDance</option>
                <option value="alibaba">Alibaba</option>
                <option value="moonshot">Moonshot</option>
                <option value="xai">XAI</option>
                <option value="deepseek">DeepSeek</option>
                <option value="siliconflow">SiliconFlow</option>
              </select>
            </div>

            <div className={styles["form-row"]}>
              <label>API地址</label>
              <Input
                value={formData.baseUrl}
                placeholder="例如：https://api.openai.com"
                onChange={(e) =>
                  setFormData({ ...formData, baseUrl: e.currentTarget.value })
                }
              />
            </div>

            <div className={styles["form-row"]}>
              <label>API密钥</label>
              <PasswordInput
                value={formData.apiKey}
                placeholder={editingProvider ? "留空表示不修改" : "输入API密钥"}
                onChange={(e) =>
                  setFormData({ ...formData, apiKey: e.currentTarget.value })
                }
              />
            </div>

            <div className={styles["form-row"]}>
              <label>描述</label>
              <Input
                value={formData.description}
                placeholder="可选的描述信息"
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    description: e.currentTarget.value,
                  })
                }
              />
            </div>

            <div className={styles["form-row"]}>
              <label>
                <input
                  type="checkbox"
                  checked={formData.enabled}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      enabled: e.currentTarget.checked,
                    })
                  }
                />
                启用此服务商
              </label>
            </div>

            <div className={styles["form-actions"]}>
              <IconButton text="取消" onClick={() => setShowModal(false)} />
              <IconButton
                text={editingProvider ? "更新" : "创建"}
                type="primary"
                onClick={handleSubmit}
              />
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
