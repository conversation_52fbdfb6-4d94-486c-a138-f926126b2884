import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserStore } from "../store/user";
import { Path } from "../constant";
import { IconButton } from "./button";
import { Modal, showConfirm } from "./ui-lib";
import Locale from "../locales";
import UserIcon from "../icons/user.svg";
import AdminIcon from "../icons/settings.svg";
import LogoutIcon from "../icons/logout.svg";
import styles from "./user-info.module.scss";

interface UserInfoProps {
  onClose?: () => void;
}

export function UserInfo({ onClose }: UserInfoProps) {
  const userStore = useUserStore();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);

  // 如果未登录，跳转到登录页面
  useEffect(() => {
    if (!userStore.isAuthenticated) {
      navigate(Path.Auth);
    }
  }, [userStore.isAuthenticated, navigate]);

  const handleLogout = () => {
    showConfirm({
      title: "确认登出",
      content: "您确定要登出当前账户吗？",
      onConfirm: async () => {
        await userStore.logout();
        navigate(Path.Home);
        onClose?.();
      },
    });
  };

  const handleAdminPanel = () => {
    navigate("/admin");
    onClose?.();
  };

  if (!userStore.user) {
    return null;
  }

  return (
    <div className={styles["user-info"]}>
      <div className={styles["user-avatar"]}>
        <UserIcon />
      </div>

      <div className={styles["user-details"]}>
        <div className={styles["user-name"]}>{userStore.user.username}</div>
        <div className={styles["user-email"]}>{userStore.user.email}</div>
        <div className={styles["user-role"]}>
          {userStore.user.role === "ADMIN" ? "管理员" : "普通用户"}
        </div>
      </div>

      <div className={styles["user-actions"]}>
        {userStore.isAdmin() && (
          <IconButton
            icon={<AdminIcon />}
            text="管理后台"
            onClick={handleAdminPanel}
            className={styles["admin-button"]}
          />
        )}

        <IconButton
          icon={<LogoutIcon />}
          text="登出"
          onClick={handleLogout}
          className={styles["logout-button"]}
        />
      </div>
    </div>
  );
}

export function UserInfoModal({
  show,
  onClose,
}: {
  show: boolean;
  onClose: () => void;
}) {
  if (!show) return null;

  return (
    <div
      className="modal-mask"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <Modal title="用户信息" onClose={onClose}>
        <UserInfo onClose={onClose} />
      </Modal>
    </div>
  );
}
