import { useState, useRef, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useUserStore } from "../store/user";
import { useSystemConfigStore } from "../store/system-config";
import { IconButton } from "./button";
import { UserInfoModal } from "./user-info";
import { Path } from "../constant";
import Locale from "../locales";

// 图标
import UserIcon from "../icons/user.svg";
import LoginIcon from "../icons/login.svg";
import AdminIcon from "../icons/settings.svg";

interface UserEntryProps {
  shouldNarrow?: boolean;
}

export function UserEntry({ shouldNarrow }: UserEntryProps) {
  const renderCountRef = useRef(0);
  renderCountRef.current++;
  console.log(`[UserEntry] Component render #${renderCountRef.current}`);

  const navigate = useNavigate();
  const userStore = useUserStore();
  const systemConfigStore = useSystemConfigStore();
  const [showUserInfo, setShowUserInfo] = useState(false);

  // 使用 useMemo 缓存 isBackendMode 的结果，避免每次渲染都调用
  const isBackendMode = useMemo(() => {
    return systemConfigStore.isBackendMode();
  }, [systemConfigStore.config]);

  console.log("[UserEntry] Render state:", {
    backendMode: isBackendMode,
    userAuthenticated: userStore.isAuthenticated,
    userRole: userStore.user?.role,
  });

  // 如果不是后端模式，不显示用户入口
  if (!isBackendMode) {
    console.log("[UserEntry] Frontend mode, hiding user entry");
    return null;
  }

  const handleUserClick = () => {
    if (userStore.isAuthenticated) {
      console.log("[UserEntry] User authenticated, showing user info");
      setShowUserInfo(true);
    } else {
      console.log("[UserEntry] User not authenticated, navigating to login");
      navigate(Path.UserAuth);
    }
  };

  const handleAdminClick = () => {
    console.log("[UserEntry] Navigating to admin panel");
    // 使用浏览器原生导航到管理后台
    window.location.href = Path.Admin;
  };

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
      {/* 用户登录/信息按钮 */}
      <IconButton
        icon={userStore.isAuthenticated ? <UserIcon /> : <LoginIcon />}
        text={
          shouldNarrow
            ? undefined
            : userStore.isAuthenticated
            ? userStore.user?.username || "用户"
            : "登录/注册"
        }
        onClick={handleUserClick}
        shadow
        style={{
          background: userStore.isAuthenticated
            ? "var(--primary-alpha)"
            : undefined,
          color: userStore.isAuthenticated ? "var(--primary)" : undefined,
        }}
      />

      {/* 管理后台按钮（仅管理员可见） */}
      {userStore.isAdmin() && (
        <IconButton
          icon={<AdminIcon />}
          text={shouldNarrow ? undefined : "管理后台"}
          onClick={handleAdminClick}
          shadow
          style={{
            background: "var(--orange-alpha)",
            color: "var(--orange)",
          }}
        />
      )}

      {/* 用户信息模态框 */}
      <UserInfoModal
        show={showUserInfo}
        onClose={() => setShowUserInfo(false)}
      />
    </div>
  );
}

/**
 * 配置模式提示组件
 * 在前端模式下显示配置提示
 */
export function ConfigModeIndicator({ shouldNarrow }: UserEntryProps) {
  const systemConfigStore = useSystemConfigStore();
  const isBackendMode = systemConfigStore.isBackendMode();
  const isFrontendConfigDisabled = systemConfigStore.isFrontendConfigDisabled();

  // 只在前端模式下显示
  if (isBackendMode) {
    return null;
  }

  return (
    <div
      style={{
        padding: "8px",
        background: "var(--gray-alpha)",
        borderRadius: "8px",
        fontSize: "12px",
        color: "var(--gray)",
        textAlign: "center",
      }}
    >
      {shouldNarrow ? "前端模式" : "前端配置模式"}
    </div>
  );
}
