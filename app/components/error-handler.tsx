import { useEffect, useState } from "react";
import { useSystemConfigStore } from "../store/system-config";
import { useUserStore } from "../store/user";
import { showToast } from "./ui-lib";

interface ErrorState {
  configError: string | null;
  authError: string | null;
  networkError: string | null;
}

/**
 * 全局错误处理组件
 * 监听各种错误状态并提供用户友好的提示
 */
export function GlobalErrorHandler() {
  const systemConfigStore = useSystemConfigStore();
  const userStore = useUserStore();
  const [errorState, setErrorState] = useState<ErrorState>({
    configError: null,
    authError: null,
    networkError: null,
  });

  // 监听配置加载错误
  useEffect(() => {
    if (
      systemConfigStore.error &&
      systemConfigStore.error !== errorState.configError
    ) {
      console.error("[ErrorHandler] Config error:", systemConfigStore.error);

      setErrorState((prev) => ({
        ...prev,
        configError: systemConfigStore.error,
      }));

      showToast(
        "配置加载失败：系统配置加载失败，部分功能可能不可用。请刷新页面重试。",
        undefined,
        5000,
      );
    }
  }, [systemConfigStore.error, errorState.configError]);

  // 监听用户认证错误
  useEffect(() => {
    if (userStore.error && userStore.error !== errorState.authError) {
      console.error("[ErrorHandler] Auth error:", userStore.error);

      setErrorState((prev) => ({ ...prev, authError: userStore.error }));

      // 根据错误类型显示不同提示
      if (
        userStore.error.includes("token") ||
        userStore.error.includes("unauthorized")
      ) {
        showToast(
          "登录已过期：您的登录状态已过期，请重新登录。",
          undefined,
          4000,
        );
      } else {
        showToast(`认证失败：${userStore.error}`, undefined, 4000);
      }
    }
  }, [userStore.error, errorState.authError]);

  // 监听网络错误
  useEffect(() => {
    const handleOnlineStatus = () => {
      if (!navigator.onLine) {
        console.warn("[ErrorHandler] Network offline");
        setErrorState((prev) => ({ ...prev, networkError: "offline" }));
        showToast("网络连接断开：请检查您的网络连接。", undefined, 3000);
      } else if (errorState.networkError === "offline") {
        console.log("[ErrorHandler] Network back online");
        setErrorState((prev) => ({ ...prev, networkError: null }));
        showToast("网络已恢复：网络连接已恢复正常。", undefined, 2000);
      }
    };

    window.addEventListener("online", handleOnlineStatus);
    window.addEventListener("offline", handleOnlineStatus);

    return () => {
      window.removeEventListener("online", handleOnlineStatus);
      window.removeEventListener("offline", handleOnlineStatus);
    };
  }, [errorState.networkError]);

  return null; // 这个组件不渲染任何UI
}

/**
 * 模式切换提示组件
 * 在模式切换时显示友好的提示信息
 */
export function ModeTransitionNotifier() {
  const systemConfigStore = useSystemConfigStore();
  const userStore = useUserStore();
  const [lastMode, setLastMode] = useState<boolean | null>(null);
  const [lastAuthState, setLastAuthState] = useState<boolean | null>(null);

  // 监听后端模式切换
  useEffect(() => {
    const currentMode = systemConfigStore.isBackendMode();

    if (lastMode !== null && lastMode !== currentMode) {
      console.log("[ModeTransition] Mode changed:", {
        from: lastMode,
        to: currentMode,
      });

      if (currentMode) {
        showToast(
          "已切换到后端模式：现在使用后端管理的配置和服务商。请登录以获得完整功能。",
          undefined,
          4000,
        );
      } else {
        showToast(
          "已切换到前端模式：现在使用本地配置。您可以在设置中配置API密钥。",
          undefined,
          4000,
        );
      }
    }

    setLastMode(currentMode);
  }, [systemConfigStore.config?.backendMode, lastMode]);

  // 监听用户登录状态变化
  useEffect(() => {
    const currentAuthState = userStore.isAuthenticated;

    if (
      lastAuthState !== null &&
      lastAuthState !== currentAuthState &&
      systemConfigStore.isBackendMode()
    ) {
      console.log("[ModeTransition] Auth state changed:", {
        from: lastAuthState,
        to: currentAuthState,
      });

      if (currentAuthState) {
        showToast(
          `登录成功：欢迎回来，${userStore.user?.username}！`,
          undefined,
          3000,
        );
      } else {
        showToast("已登出：您已成功登出。部分功能将不可用。", undefined, 3000);
      }
    }

    setLastAuthState(currentAuthState);
  }, [
    userStore.isAuthenticated,
    userStore.user?.username,
    lastAuthState,
    systemConfigStore.isBackendMode(),
  ]);

  return null; // 这个组件不渲染任何UI
}

/**
 * 性能监控组件
 * 监控应用性能并在必要时提供优化建议
 */
export function PerformanceMonitor() {
  useEffect(() => {
    // 监控页面加载性能
    if (typeof window !== "undefined" && "performance" in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();

        entries.forEach((entry) => {
          if (entry.entryType === "navigation") {
            const navEntry = entry as PerformanceNavigationTiming;
            const loadTime = navEntry.loadEventEnd - navEntry.loadEventStart;

            console.log("[Performance] Page load time:", loadTime + "ms");

            // 如果加载时间超过3秒，显示提示
            if (loadTime > 3000) {
              showToast(
                "页面加载较慢：网络连接可能较慢，建议检查网络状况。",
                undefined,
                3000,
              );
            }
          }
        });
      });

      observer.observe({ entryTypes: ["navigation"] });

      return () => {
        observer.disconnect();
      };
    }
  }, []);

  return null; // 这个组件不渲染任何UI
}
