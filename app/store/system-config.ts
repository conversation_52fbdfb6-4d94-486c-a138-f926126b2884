import { create } from "zustand";

export interface SystemConfig {
  // 基础配置
  needCode: boolean;
  hideUserApiKey: boolean;
  disableGPT4: boolean;
  hideBalanceQuery: boolean;
  disableFastLink: boolean;
  customModels: string;
  defaultModel: string;
  visionModels: string;

  // 后端模式配置
  backendMode?: boolean;
  frontendConfigDisabled?: boolean;
  providers?: ProviderInfo[];
}

export interface ProviderInfo {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  models?: string[];
}

interface SystemConfigState {
  config: SystemConfig | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
}

interface SystemConfigActions {
  loadConfig: () => Promise<void>;
  setConfig: (config: SystemConfig) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  isBackendMode: () => boolean;
  isFrontendConfigDisabled: () => boolean;
  getAvailableProviders: () => ProviderInfo[];
  refreshConfig: () => Promise<void>;
}

const DEFAULT_SYSTEM_CONFIG: SystemConfig = {
  needCode: false,
  hideUserApiKey: false,
  disableGPT4: false,
  hideBalanceQuery: false,
  disableFastLink: false,
  customModels: "",
  defaultModel: "",
  visionModels: "",
  backendMode: false,
  frontendConfigDisabled: false,
  providers: [],
};

export const useSystemConfigStore = create<
  SystemConfigState & SystemConfigActions
>((set, get) => ({
  config: null,
  isLoading: false,
  error: null,
  lastUpdated: 0,

  loadConfig: async () => {
    const { isLoading } = get();
    if (isLoading) return; // 防止重复加载

    set({ isLoading: true, error: null });

    try {
      console.log("[SystemConfig] Loading system configuration...");

      const response = await fetch("/api/config", {
        method: "GET",
        headers: {
          "Cache-Control": "no-cache",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const config = await response.json();

      console.log("[SystemConfig] Loaded configuration:", {
        backendMode: config.backendMode,
        frontendConfigDisabled: config.frontendConfigDisabled,
        providersCount: config.providers?.length || 0,
        hideUserApiKey: config.hideUserApiKey,
        fullConfig: config,
      });

      set({
        config,
        isLoading: false,
        error: null,
        lastUpdated: Date.now(),
      });
    } catch (error) {
      console.error("[SystemConfig] Failed to load configuration:", error);

      set({
        config: DEFAULT_SYSTEM_CONFIG,
        isLoading: false,
        error: error instanceof Error ? error.message : "加载配置失败",
        lastUpdated: Date.now(),
      });
    }
  },

  setConfig: (config: SystemConfig) => {
    console.log("[SystemConfig] Configuration updated:", {
      backendMode: config.backendMode,
      frontendConfigDisabled: config.frontendConfigDisabled,
      hideUserApiKey: config.hideUserApiKey,
    });

    set({
      config,
      lastUpdated: Date.now(),
    });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  isBackendMode: () => {
    const { config } = get();
    const result = config?.backendMode === true;
    console.log(
      "[SystemConfig] Backend mode check:",
      result,
      "config:",
      config,
    );
    return result;
  },

  isFrontendConfigDisabled: () => {
    const { config } = get();
    const result = config?.frontendConfigDisabled === true;
    console.log("[SystemConfig] Frontend config disabled check:", result);
    return result;
  },

  getAvailableProviders: () => {
    const { config } = get();
    const providers = config?.providers || [];
    console.log("[SystemConfig] Available providers:", providers.length);
    return providers;
  },

  refreshConfig: async () => {
    console.log("[SystemConfig] Refreshing configuration...");
    await get().loadConfig();
  },
}));
