import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

console.log("[UserStore] Imports loaded successfully");

export interface User {
  id: string;
  username: string;
  email: string;
  role: "USER" | "ADMIN";
  enabled: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface UserState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface UserActions {
  setUser: (user: User, token: string) => void;
  clearUser: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (username: string, password: string) => Promise<boolean>;
  register: (
    username: string,
    email: string,
    password: string,
  ) => Promise<boolean>;
  logout: () => Promise<void>;
  fetchUserInfo: () => Promise<boolean>;
  isAdmin: () => boolean;
}

const DEFAULT_STATE: UserState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

console.log("[UserStore] Creating user store...");

// 先尝试创建一个简单的store来测试
const createUserStore = () => {
  console.log("[UserStore] Store factory function called");

  try {
    return create<UserState & UserActions>()(
      persist(
        (set, get) => ({
          ...DEFAULT_STATE,
      setUser: (user: User, token: string) => {
        set({
          user,
          token,
          isAuthenticated: true,
          error: null,
        });
      },

      clearUser: () => {
        set(DEFAULT_STATE);
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await fetch("/api/auth/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ username, password }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            get().setUser(data.user, data.token);
            return true;
          } else {
            set({ error: data.error || "登录失败" });
            return false;
          }
        } catch (error) {
          console.error("Login error:", error);
          set({ error: "网络错误，请稍后重试" });
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      register: async (username: string, email: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await fetch("/api/auth/register", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ username, email, password }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            get().setUser(data.user, data.token);
            return true;
          } else {
            set({ error: data.error || "注册失败" });
            return false;
          }
        } catch (error) {
          console.error("Register error:", error);
          set({ error: "网络错误，请稍后重试" });
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      logout: async () => {
        try {
          await fetch("/api/auth/logout", {
            method: "POST",
          });
        } catch (error) {
          console.error("Logout error:", error);
        } finally {
          get().clearUser();
        }
      },

      fetchUserInfo: async () => {
        const { token } = get();
        if (!token) {
          return false;
        }

        try {
          const response = await fetch("/api/auth/me", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          const data = await response.json();

          if (response.ok && data.success) {
            set({
              user: data.user,
              isAuthenticated: true,
              error: null,
            });
            return true;
          } else {
            get().clearUser();
            return false;
          }
        } catch (error) {
          console.error("Fetch user info error:", error);
          get().clearUser();
          return false;
        }
      },

        isAdmin: () => {
          const { user } = get();
          return user?.role === "ADMIN";
        },
      }),
      {
        name: "user-store",
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
        }),
        onRehydrateStorage: () => (state) => {
          console.log("[UserStore] Hydration completed");
        },
      },
    );
  } catch (error) {
    console.error("[UserStore] Failed to create store:", error);
    throw error;
  }
};

export const useUserStore = createUserStore();
