"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../store/user";
import styles from "./admin.module.scss";

export default function AdminPage() {
  const router = useRouter();
  const userStore = useUserStore();
  const [isHydrated, setIsHydrated] = useState(false);

  // 等待 hydration 完成
  useEffect(() => {
    if (typeof window !== "undefined") {
      const timer = setTimeout(() => {
        setIsHydrated(true);
      }, 200); // 增加延迟时间
      return () => clearTimeout(timer);
    }
  }, []);

  // 如果还没有 hydration 完成，显示加载状态
  if (typeof window === "undefined" || !isHydrated) {
    return (
      <div className={styles["loading-container"]}>
        <div className={styles["loading-content"]}>
          <div className={styles["loading-spinner"]}></div>
          <div>正在加载管理后台...</div>
        </div>
      </div>
    );
  }

  // 简单的权限检查（不重定向，只显示信息）
  const isAuthenticated = userStore.isAuthenticated;
  const isAdmin = userStore.isAdmin();
  const username = userStore.user?.username;

  // 如果权限不足，显示错误页面
  if (!isAuthenticated || !isAdmin) {
    return (
      <div className={styles["error-container"]}>
        <h1 className={styles["error-title"]}>访问受限</h1>
        <p className={styles["error-message"]}>
          ❌ 权限不足，请先登录管理员账户
        </p>
        <button
          className={styles["error-button"]}
          onClick={() => router.push("/user-auth")}
        >
          去登录
        </button>
      </div>
    );
  }

  return (
    <div className={styles["admin-container"]}>
      {/* 左侧菜单 */}
      <div className={styles["sidebar"]}>
        <div className={styles["sidebar-header"]}>
          <h2 className={styles["sidebar-title"]}>QADChat 管理后台</h2>
          <p className={styles["sidebar-subtitle"]}>欢迎，{username}</p>
        </div>

        <nav className={styles["sidebar-nav"]}>
          <div className={styles["nav-item"]}>
            <button className={`${styles["nav-button"]} ${styles["active"]}`}>
              <span className={styles["nav-icon"]}>📊</span>
              仪表盘
            </button>
          </div>
          <div className={styles["nav-item"]}>
            <button
              className={styles["nav-button"]}
              onClick={() => router.push("/admin/providers")}
            >
              <span className={styles["nav-icon"]}>⚙️</span>
              服务商管理
            </button>
          </div>
          <div className={styles["nav-item"]}>
            <button className={styles["nav-button"]}>
              <span className={styles["nav-icon"]}>👥</span>
              用户管理
            </button>
          </div>
          <div className={styles["nav-item"]}>
            <button className={styles["nav-button"]}>
              <span className={styles["nav-icon"]}>📝</span>
              调用日志
            </button>
          </div>
          <div className={styles["nav-item"]}>
            <button className={styles["nav-button"]}>
              <span className={styles["nav-icon"]}>⚙️</span>
              系统设置
            </button>
          </div>
        </nav>

        <div className={styles["sidebar-footer"]}>
          <button
            className={styles["footer-button"]}
            onClick={() => router.push("/")}
          >
            ← 返回聊天
          </button>
          <button
            className={`${styles["footer-button"]} ${styles["logout"]}`}
            onClick={() => userStore.logout()}
          >
            登出
          </button>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className={styles["main-content"]}>
        <div className={styles["content-header"]}>
          <h1 className={styles["content-title"]}>仪表盘</h1>
          <p className={styles["content-subtitle"]}>
            欢迎使用 QADChat 管理后台
          </p>
        </div>

        <div className={styles["content-body"]}>
          <div className={styles["dashboard-grid"]}>
            <div className={styles["dashboard-card"]}>
              <div className={`${styles["card-icon"]} ${styles["users"]}`}>
                👥
              </div>
              <h3 className={styles["card-title"]}>用户管理</h3>
              <p className={styles["card-description"]}>
                管理系统用户，查看用户信息，设置用户权限和状态
              </p>
            </div>

            <div className={styles["dashboard-card"]}>
              <div className={`${styles["card-icon"]} ${styles["providers"]}`}>
                ⚙️
              </div>
              <h3 className={styles["card-title"]}>服务商配置</h3>
              <p className={styles["card-description"]}>
                配置和管理AI服务商，设置API密钥，监控服务状态
              </p>
            </div>

            <div className={styles["dashboard-card"]}>
              <div className={`${styles["card-icon"]} ${styles["stats"]}`}>
                📊
              </div>
              <h3 className={styles["card-title"]}>使用统计</h3>
              <p className={styles["card-description"]}>
                查看系统使用数据，分析用户行为，监控资源消耗
              </p>
            </div>

            <div className={styles["dashboard-card"]}>
              <div className={`${styles["card-icon"]} ${styles["logs"]}`}>
                📝
              </div>
              <h3 className={styles["card-title"]}>调用日志</h3>
              <p className={styles["card-description"]}>
                查看API调用记录，分析请求模式，排查系统问题
              </p>
            </div>
          </div>

          <div className={styles["status-card"]}>
            <h3 className={styles["status-title"]}>系统状态</h3>
            <div className={styles["status-item"]}>
              <div className={`${styles["status-icon"]} ${styles["success"]}`}>
                ✓
              </div>
              <span>系统运行正常</span>
            </div>
            <div className={styles["status-item"]}>
              <div className={`${styles["status-icon"]} ${styles["success"]}`}>
                ✓
              </div>
              <span>数据库连接正常</span>
            </div>
            <div className={styles["status-item"]}>
              <div className={`${styles["status-icon"]} ${styles["success"]}`}>
                ✓
              </div>
              <span>用户认证服务正常</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
