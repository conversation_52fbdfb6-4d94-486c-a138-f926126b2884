/* 现代化管理后台样式 */

.admin-container {
  display: flex;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
}

.sidebar {
  width: 280px;
  background: #ffffff;
  color: #1d1d1f;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #d2d2d7;
  position: relative;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #d2d2d7;
}

.sidebar-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1d1d1f;
  letter-spacing: -0.01em;
}

.sidebar-subtitle {
  font-size: 14px;
  color: #86868b;
  margin: 0;
  font-weight: 400;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  margin: 0 16px 8px 16px;
}

.nav-button {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  background: transparent;
  color: #1d1d1f;
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-button:hover {
  background: #f5f5f7;
  color: #1d1d1f;
}

.nav-button.active {
  background: #007aff;
  color: white;
  font-weight: 500;
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.sidebar-footer {
  padding: 20px 16px;
  border-top: 1px solid #d2d2d7;
}

.footer-button {
  width: 100%;
  padding: 10px 16px;
  margin-bottom: 8px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  background: transparent;
  color: #1d1d1f;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
  font-weight: 400;
}

.footer-button:hover {
  background: #f5f5f7;
}

.footer-button.logout {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

.footer-button.logout:hover {
  background: #d70015;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.content-subtitle {
  color: #64748b;
  margin: 0;
  font-size: 16px;
}

.content-body {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background: #f8fafc;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.dashboard-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

.card-icon.users {
  background: #007aff;
  color: white;
}

.card-icon.providers {
  background: #34c759;
  color: white;
}

.card-icon.stats {
  background: #ff9500;
  color: white;
}

.card-icon.logs {
  background: #af52de;
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.card-description {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.status-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.status-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  font-size: 14px;
}

.status-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.status-icon.success {
  background: #10b981;
  color: white;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8fafc;
}

.loading-content {
  text-align: center;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8fafc;
  flex-direction: column;
  gap: 24px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.error-message {
  color: #ef4444;
  font-size: 16px;
  margin: 0;
}

.error-button {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.error-button:hover {
  background: #2563eb;
}

/* 服务商管理样式 */
.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.provider-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #d2d2d7;
  transition: all 0.15s ease;
}

.provider-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.provider-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.provider-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  border-radius: 12px;
  flex-shrink: 0;
}

.provider-info {
  flex: 1;
  min-width: 0;
}

.provider-name {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.provider-description {
  font-size: 14px;
  color: #86868b;
  margin: 0;
  line-height: 1.4;
}

.provider-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.enabled {
  background: #d1f2eb;
  color: #00875a;
}

.status-badge.disabled {
  background: #ffebe6;
  color: #de350b;
}

.status-badge.not-configured {
  background: #f4f5f7;
  color: #6b778c;
}

.provider-form {
  border-top: 1px solid #f5f5f7;
  padding-top: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.15s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-hint {
  display: block;
  font-size: 12px;
  color: #86868b;
  margin-top: 4px;
  line-height: 1.3;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.provider-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-primary {
  padding: 10px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: #0056cc;
}

.btn-primary:disabled {
  background: #d2d2d7;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 10px 20px;
  background: transparent;
  color: #1d1d1f;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary:hover {
  background: #f5f5f7;
}

.btn-success {
  color: #00875a;
  border-color: #00875a;
}

.btn-success:hover {
  background: #e3fcef;
}

.btn-warning {
  color: #de350b;
  border-color: #de350b;
}

.btn-warning:hover {
  background: #ffebe6;
}

/* 模型管理样式 */
.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  background: #f5f5f7;
}

.model-checkbox {
  display: flex !important;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 13px;
  border: 1px solid transparent;
}

.model-checkbox:hover {
  background: #f0f0f0;
  border-color: #d2d2d7;
}

.model-checkbox input[type="checkbox"] {
  margin: 0;
  flex-shrink: 0;
}

.model-checkbox input[type="checkbox"]:checked + .model-name {
  font-weight: 500;
  color: #007aff;
}

.model-name {
  flex: 1;
  min-width: 0;
  word-break: break-all;
  line-height: 1.3;
}

.no-models {
  text-align: center;
  color: #86868b;
  font-style: italic;
  padding: 20px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .providers-grid {
    grid-template-columns: 1fr;
  }

  .content-body {
    padding: 16px;
  }
}
