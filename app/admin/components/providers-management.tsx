import { useState, useEffect } from "react";
import { ServiceProvider } from "../../constant";
import styles from "../admin.module.scss";

// 导入模型列表
const openaiModels = [
  "gpt-3.5-turbo",
  "gpt-4",
  "gpt-4-turbo",
  "gpt-4o",
  "gpt-4o-mini",
  "o1-preview",
  "o1-mini",
  "o3-mini",
  "text-embedding-3-large",
  "dall-e-3",
];

const googleModels = [
  "gemini-2.5-pro",
  "gemini-2.5-flash",
  "gemini-1.5-pro",
  "gemini-1.5-flash",
  "gemini-pro",
  "gemini-pro-vision",
  "text-embedding-004",
];

const anthropicModels = [
  "claude-3-5-sonnet-20241022",
  "claude-3-5-haiku-20241022",
  "claude-3-opus-20240229",
  "claude-3-sonnet-20240229",
  "claude-3-haiku-20240307",
];

const bytedanceModels = [
  "doubao-pro-32k",
  "doubao-lite-32k",
  "doubao-vision-lite-32k",
  "doubao-embedding-large-text",
  "deepseek-r1",
];

const alibabaModels = [
  "qwen-max",
  "qwen-plus",
  "qwen2.5-72b-instruct",
  "qwen2.5-32b-instruct",
  "qwen2.5-7b-instruct",
  "qwen-vl-plus",
  "qwq-32b-preview",
];

const moonshotModels = [
  "moonshot-v1-8k",
  "moonshot-v1-32k",
  "moonshot-v1-128k",
];

const xaiModels = ["grok-beta", "grok-vision-beta"];

const deepseekModels = [
  "deepseek-chat",
  "deepseek-coder",
  "deepseek-r1",
  "deepseek-v3",
];

const siliconflowModels = [
  "deepseek-v2.5",
  "qwen2.5-72b-instruct",
  "llama-3.1-405b-instruct",
  "yi-lightning",
  "glm-4-9b-chat",
  "internlm2_5-7b-chat",
];

// 模型映射
const PROVIDER_MODELS: Record<string, string[]> = {
  openai: openaiModels,
  google: googleModels,
  anthropic: anthropicModels,
  bytedance: bytedanceModels,
  alibaba: alibabaModels,
  moonshot: moonshotModels,
  xai: xaiModels,
  deepseek: deepseekModels,
  siliconflow: siliconflowModels,
};

interface ProviderConfig {
  id: string;
  name: string;
  type: string;
  baseUrl: string;
  enabled: boolean;
  description?: string;
  hasApiKey: boolean;
  models?: string[];
}

interface ProviderFormData {
  apiKey: string;
  baseUrl: string;
  enabled: boolean;
  models: string[];
}

// 内置服务商配置
const BUILTIN_PROVIDERS = [
  {
    id: "openai",
    name: "OpenAI",
    type: "openai",
    baseUrl: "https://api.openai.com",
    description: "OpenAI GPT 系列模型，包括 GPT-4、GPT-3.5 等",
    icon: "🤖",
  },
  {
    id: "google",
    name: "Google",
    type: "google",
    baseUrl: "https://generativelanguage.googleapis.com",
    description: "Google Gemini 系列模型",
    icon: "🔍",
  },
  {
    id: "anthropic",
    name: "Anthropic",
    type: "anthropic",
    baseUrl: "https://api.anthropic.com",
    description: "Anthropic Claude 系列模型",
    icon: "🧠",
  },
  {
    id: "bytedance",
    name: "ByteDance",
    type: "bytedance",
    baseUrl: "https://ark.cn-beijing.volces.com",
    description: "字节跳动豆包系列模型",
    icon: "🎵",
  },
  {
    id: "alibaba",
    name: "Alibaba",
    type: "alibaba",
    baseUrl: "https://dashscope.aliyuncs.com/api",
    description: "阿里云通义千问系列模型",
    icon: "☁️",
  },
  {
    id: "moonshot",
    name: "Moonshot",
    type: "moonshot",
    baseUrl: "https://api.moonshot.cn",
    description: "月之暗面 Kimi 系列模型",
    icon: "🌙",
  },
  {
    id: "xai",
    name: "XAI",
    type: "xai",
    baseUrl: "https://api.x.ai",
    description: "xAI Grok 系列模型",
    icon: "❌",
  },
  {
    id: "deepseek",
    name: "DeepSeek",
    type: "deepseek",
    baseUrl: "https://api.deepseek.com",
    description: "DeepSeek 系列模型",
    icon: "🔬",
  },
  {
    id: "siliconflow",
    name: "SiliconFlow",
    type: "siliconflow",
    baseUrl: "https://api.siliconflow.cn",
    description: "硅基流动开源模型服务",
    icon: "💎",
  },
];

export function ProvidersManagement() {
  const [providers, setProviders] = useState<ProviderConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingProvider, setEditingProvider] = useState<string | null>(null);
  const [showModelManager, setShowModelManager] = useState<string | null>(null);
  const [formData, setFormData] = useState<ProviderFormData>({
    apiKey: "",
    baseUrl: "",
    enabled: true,
    models: [],
  });

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/providers");
      const data = await response.json();

      if (data.success) {
        // 合并内置服务商配置和数据库中的配置
        const dbProviders = data.providers || [];

        const mergedProviders = BUILTIN_PROVIDERS.map((builtin) => {
          const dbProvider = dbProviders.find(
            (p: any) => p.type === builtin.type,
          );
          return {
            ...builtin,
            enabled: dbProvider?.enabled || false,
            hasApiKey: !!dbProvider?.id,
            id: dbProvider?.id || builtin.id,
            baseUrl: dbProvider?.baseUrl || builtin.baseUrl,
            models: dbProvider?.models || [],
          };
        });

        setProviders(mergedProviders);
      } else {
        console.error("Failed to load providers:", data.error);
        // 使用默认配置
        setProviders(
          BUILTIN_PROVIDERS.map((p) => ({
            ...p,
            enabled: false,
            hasApiKey: false,
            models: [],
          })),
        );
      }
    } catch (error) {
      console.error("Failed to load providers:", error);
      setProviders(
        BUILTIN_PROVIDERS.map((p) => ({
          ...p,
          enabled: false,
          hasApiKey: false,
          models: [],
        })),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (provider: ProviderConfig) => {
    setEditingProvider(provider.id);
    setFormData({
      apiKey: "",
      baseUrl: provider.baseUrl,
      enabled: provider.enabled,
      models: provider.models || [],
    });
  };

  const handleManageModels = (provider: ProviderConfig) => {
    setShowModelManager(provider.id);
    setFormData({
      apiKey: "",
      baseUrl: provider.baseUrl,
      enabled: provider.enabled,
      models: provider.models || [],
    });
  };

  const handleSave = async (provider: ProviderConfig) => {
    try {
      const url = provider.hasApiKey
        ? `/api/providers/${provider.id}`
        : "/api/providers";

      const method = provider.hasApiKey ? "PUT" : "POST";

      const requestData = {
        name: provider.name,
        type: provider.type,
        baseUrl: formData.baseUrl,
        apiKey: formData.apiKey,
        enabled: formData.enabled,
        models: formData.models,
        description: provider.description,
      };

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        setEditingProvider(null);
        await loadProviders();
      } else {
        const data = await response.json();
        console.error("Failed to save provider:", data.error);
        alert("保存失败：" + (data.error || "未知错误"));
      }
    } catch (error) {
      console.error("Failed to save provider:", error);
      alert("保存失败：网络错误");
    }
  };

  const handleCancel = () => {
    setEditingProvider(null);
    setShowModelManager(null);
    setFormData({ apiKey: "", baseUrl: "", enabled: true, models: [] });
  };

  const handleSaveModels = async (provider: ProviderConfig) => {
    try {
      const response = await fetch(`/api/providers/${provider.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          models: formData.models,
        }),
      });

      if (response.ok) {
        setShowModelManager(null);
        await loadProviders();
      } else {
        const data = await response.json();
        console.error("Failed to save models:", data.error);
        alert("保存失败：" + (data.error || "未知错误"));
      }
    } catch (error) {
      console.error("Failed to save models:", error);
      alert("保存失败：网络错误");
    }
  };

  const handleModelToggle = (modelId: string) => {
    const newModels = formData.models.includes(modelId)
      ? formData.models.filter((m) => m !== modelId)
      : [...formData.models, modelId];
    setFormData({ ...formData, models: newModels });
  };

  const handleToggleEnabled = async (provider: ProviderConfig) => {
    if (!provider.hasApiKey) {
      alert("请先配置API密钥");
      return;
    }

    try {
      const response = await fetch(`/api/providers/${provider.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          enabled: !provider.enabled,
        }),
      });

      if (response.ok) {
        await loadProviders();
      } else {
        const data = await response.json();
        console.error("Failed to toggle provider:", data.error);
        alert("操作失败：" + (data.error || "未知错误"));
      }
    } catch (error) {
      console.error("Failed to toggle provider:", error);
      alert("操作失败：网络错误");
    }
  };

  if (loading) {
    return (
      <div className={styles["loading-container"]}>
        <div className={styles["loading-content"]}>
          <div className={styles["loading-spinner"]}></div>
          <div>正在加载服务商配置...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles["providers-grid"]}>
      {providers.map((provider) => (
        <div key={provider.id} className={styles["provider-card"]}>
          <div className={styles["provider-header"]}>
            <div className={styles["provider-icon"]}>
              {
                (BUILTIN_PROVIDERS.find((p) => p.id === provider.type) as any)
                  ?.icon
              }
            </div>
            <div className={styles["provider-info"]}>
              <h3 className={styles["provider-name"]}>{provider.name}</h3>
              <p className={styles["provider-description"]}>
                {provider.description}
              </p>
            </div>
            <div className={styles["provider-status"]}>
              {provider.hasApiKey ? (
                <span
                  className={`${styles["status-badge"]} ${
                    provider.enabled ? styles["enabled"] : styles["disabled"]
                  }`}
                >
                  {provider.enabled ? "已启用" : "已禁用"}
                </span>
              ) : (
                <span
                  className={`${styles["status-badge"]} ${styles["not-configured"]}`}
                >
                  未配置
                </span>
              )}
            </div>
          </div>

          {editingProvider === provider.id ? (
            <div className={styles["provider-form"]}>
              <div className={styles["form-group"]}>
                <label>API 密钥</label>
                <input
                  type="password"
                  value={formData.apiKey}
                  placeholder="请输入API密钥"
                  onChange={(e) =>
                    setFormData({ ...formData, apiKey: e.target.value })
                  }
                  className={styles["form-input"]}
                />
              </div>
              <div className={styles["form-group"]}>
                <label>Base URL</label>
                <input
                  type="text"
                  value={formData.baseUrl}
                  placeholder="请输入自定义Base URL"
                  onChange={(e) =>
                    setFormData({ ...formData, baseUrl: e.target.value })
                  }
                  className={styles["form-input"]}
                />
                <small className={styles["form-hint"]}>
                  留空使用默认URL:{" "}
                  {
                    BUILTIN_PROVIDERS.find((p) => p.type === provider.type)
                      ?.baseUrl
                  }
                </small>
              </div>
              <div className={styles["form-group"]}>
                <label className={styles["checkbox-label"]}>
                  <input
                    type="checkbox"
                    checked={formData.enabled}
                    onChange={(e) =>
                      setFormData({ ...formData, enabled: e.target.checked })
                    }
                  />
                  启用此服务商
                </label>
              </div>
              <div className={styles["form-actions"]}>
                <button
                  className={styles["btn-primary"]}
                  onClick={() => handleSave(provider)}
                  disabled={!formData.apiKey.trim()}
                >
                  保存
                </button>
                <button
                  className={styles["btn-secondary"]}
                  onClick={handleCancel}
                >
                  取消
                </button>
              </div>
            </div>
          ) : showModelManager === provider.id ? (
            <div className={styles["provider-form"]}>
              <div className={styles["form-group"]}>
                <label>启用的模型 ({formData.models.length} 个已选择)</label>
                <div className={styles["models-grid"]}>
                  {(PROVIDER_MODELS[provider.type] || []).map((model) => (
                    <label key={model} className={styles["model-checkbox"]}>
                      <input
                        type="checkbox"
                        checked={formData.models.includes(model)}
                        onChange={() => handleModelToggle(model)}
                      />
                      <span className={styles["model-name"]}>{model}</span>
                    </label>
                  ))}
                </div>
                {(PROVIDER_MODELS[provider.type] || []).length === 0 && (
                  <p className={styles["no-models"]}>
                    该服务商暂无可配置的模型
                  </p>
                )}
              </div>
              <div className={styles["form-actions"]}>
                <button
                  className={styles["btn-primary"]}
                  onClick={() => handleSaveModels(provider)}
                >
                  保存模型配置
                </button>
                <button
                  className={styles["btn-secondary"]}
                  onClick={handleCancel}
                >
                  取消
                </button>
              </div>
            </div>
          ) : (
            <div className={styles["provider-actions"]}>
              <button
                className={styles["btn-secondary"]}
                onClick={() => handleEdit(provider)}
              >
                {provider.hasApiKey ? "编辑配置" : "配置API密钥"}
              </button>
              {provider.hasApiKey && (
                <>
                  <button
                    className={styles["btn-secondary"]}
                    onClick={() => handleManageModels(provider)}
                  >
                    管理模型 ({provider.models?.length || 0})
                  </button>
                  <button
                    className={`${styles["btn-secondary"]} ${
                      provider.enabled
                        ? styles["btn-warning"]
                        : styles["btn-success"]
                    }`}
                    onClick={() => handleToggleEnabled(provider)}
                  >
                    {provider.enabled ? "禁用" : "启用"}
                  </button>
                </>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
