"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../../store/user";
import { AdminLayout } from "../../components/admin/admin-layout";
import { ProvidersManagement } from "../components/providers-management";

export default function AdminProvidersPage() {
  const router = useRouter();
  const userStore = useUserStore();
  const [isHydrated, setIsHydrated] = useState(false);

  // 等待 hydration 完成
  useEffect(() => {
    if (typeof window !== "undefined") {
      const timer = setTimeout(() => {
        setIsHydrated(true);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, []);

  // 如果还没有 hydration 完成，显示加载状态
  if (typeof window === "undefined" || !isHydrated) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <div>正在加载服务商管理...</div>
      </div>
    );
  }

  // 权限检查
  const isAuthenticated = userStore.isAuthenticated;
  const isAdmin = userStore.isAdmin();
  const username = userStore.user?.username;

  // 如果权限不足，显示错误页面
  if (!isAuthenticated || !isAdmin) {
    return (
      <div style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        flexDirection: "column",
        gap: "20px",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        background: "#f8f9fa",
      }}>
        <h1 style={{ fontSize: "28px", fontWeight: "600", color: "#1d1d1f", margin: "0 0 16px 0" }}>
          访问受限
        </h1>
        <p style={{ color: "#ff3b30", fontSize: "17px", margin: "0 0 24px 0" }}>
          ❌ 权限不足，请先登录管理员账户
        </p>
        <button
          onClick={() => router.push("/user-auth")}
          style={{
            background: "#007aff",
            color: "white",
            border: "none",
            padding: "12px 24px",
            borderRadius: "10px",
            fontSize: "15px",
            fontWeight: "500",
            cursor: "pointer",
            transition: "all 0.2s ease",
          }}
        >
          去登录
        </button>
      </div>
    );
  }

  return (
    <AdminLayout>
      <ProvidersManagement />
    </AdminLayout>
  );
}
