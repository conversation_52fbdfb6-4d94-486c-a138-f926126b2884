"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../../store/user";
import { ProvidersManagement } from "../components/providers-management";
import styles from "../admin.module.scss";

export default function AdminProvidersPage() {
  const router = useRouter();
  const userStore = useUserStore();
  const [isHydrated, setIsHydrated] = useState(false);

  // 等待 hydration 完成
  useEffect(() => {
    if (typeof window !== "undefined") {
      const timer = setTimeout(() => {
        setIsHydrated(true);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, []);

  // 如果还没有 hydration 完成，显示加载状态
  if (typeof window === "undefined" || !isHydrated) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <div>正在加载服务商管理...</div>
      </div>
    );
  }

  // 权限检查
  const isAuthenticated = userStore.isAuthenticated;
  const isAdmin = userStore.isAdmin();
  const username = userStore.user?.username;

  // 如果权限不足，显示错误页面
  if (!isAuthenticated || !isAdmin) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          flexDirection: "column",
          gap: "20px",
        }}
      >
        <h1>访问受限</h1>
        <p style={{ color: "red" }}>❌ 权限不足，请先登录管理员账户</p>
        <button onClick={() => router.push("/user-auth")}>去登录</button>
      </div>
    );
  }

  return (
    <div style={{ display: "flex", height: "100vh" }}>
      {/* 左侧菜单 */}
      <div
        style={{
          width: "260px",
          background: "#f5f5f5",
          borderRight: "1px solid #e0e0e0",
          padding: "20px",
        }}
      >
        <div style={{ marginBottom: "30px" }}>
          <h2 style={{ margin: "0 0 10px 0" }}>QADChat 管理后台</h2>
          <p style={{ margin: 0, fontSize: "14px", color: "#666" }}>
            欢迎，{username}
          </p>
        </div>

        <nav>
          <div style={{ marginBottom: "10px" }}>
            <button
              style={{
                width: "100%",
                padding: "10px",
                textAlign: "left",
                background: "transparent",
                border: "1px solid #ddd",
                borderRadius: "5px",
              }}
              onClick={() => router.push("/admin")}
            >
              📊 仪表盘
            </button>
          </div>
          <div style={{ marginBottom: "10px" }}>
            <button
              style={{
                width: "100%",
                padding: "10px",
                textAlign: "left",
                background: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "5px",
              }}
            >
              ⚙️ 服务商管理
            </button>
          </div>
        </nav>

        <div style={{ position: "absolute", bottom: "20px", width: "220px" }}>
          <button
            onClick={() => router.push("/")}
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              background: "transparent",
              border: "1px solid #ddd",
              borderRadius: "5px",
            }}
          >
            ← 返回聊天
          </button>
          <button
            onClick={() => userStore.logout()}
            style={{
              width: "100%",
              padding: "10px",
              background: "#dc3545",
              color: "white",
              border: "none",
              borderRadius: "5px",
            }}
          >
            登出
          </button>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className={styles["main-content"]}>
        <div className={styles["content-header"]}>
          <h1 className={styles["content-title"]}>服务商管理</h1>
          <p className={styles["content-subtitle"]}>
            配置和管理AI服务商的API密钥
          </p>
        </div>

        <div className={styles["content-body"]}>
          <ProvidersManagement />
        </div>
      </div>
    </div>
  );
}
