"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../../store/user";
import { AdminLayout } from "../../components/admin/admin-layout";

export default function AdminUsersPage() {
  const router = useRouter();
  const userStore = useUserStore();
  const [isHydrated, setIsHydrated] = useState(false);

  // 等待 hydration 完成
  useEffect(() => {
    if (typeof window !== "undefined") {
      const timer = setTimeout(() => {
        setIsHydrated(true);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, []);

  // 如果还没有 hydration 完成，显示加载状态
  if (typeof window === "undefined" || !isHydrated) {
    return (
      <div style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        fontFamily: "-apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif",
        background: "#f8f9fa",
        color: "#86868b",
        fontSize: "17px",
        fontWeight: "500"
      }}>
        正在加载管理后台...
      </div>
    );
  }

  // 简单的权限检查（不重定向，只显示信息）
  const isAuthenticated = userStore.isAuthenticated;
  const isAdmin = userStore.isAdmin();

  // 如果权限不足，显示错误页面
  if (!isAuthenticated || !isAdmin) {
    return (
      <div style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        flexDirection: "column",
        gap: "20px",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        background: "#f8f9fa",
      }}>
        <h1 style={{ fontSize: "28px", fontWeight: "600", color: "#1d1d1f", margin: "0 0 16px 0" }}>
          访问受限
        </h1>
        <p style={{ color: "#ff3b30", fontSize: "17px", margin: "0 0 24px 0" }}>
          ❌ 权限不足，请先登录管理员账户
        </p>
        <button 
          onClick={() => router.push("/user-auth")}
          style={{
            background: "#007aff",
            color: "white",
            border: "none",
            padding: "12px 24px",
            borderRadius: "10px",
            fontSize: "15px",
            fontWeight: "500",
            cursor: "pointer",
            transition: "all 0.2s ease",
          }}
        >
          去登录
        </button>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div style={{
        padding: "32px",
        height: "100%",
        overflow: "auto",
        background: "#f8f9fa",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
      }}>
        <div style={{ marginBottom: "32px" }}>
          <h1 style={{
            fontSize: "34px",
            fontWeight: "700",
            color: "#1d1d1f",
            margin: "0 0 8px 0",
            letterSpacing: "-0.022em"
          }}>
            用户管理
          </h1>
          <p style={{
            fontSize: "17px",
            color: "#86868b",
            margin: "0",
            fontWeight: "400"
          }}>
            管理系统用户，查看用户信息，设置用户权限和状态
          </p>
        </div>
        
        <div style={{
          background: "#ffffff",
          borderRadius: "16px",
          border: "1px solid #e5e5e7",
          padding: "24px",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.04)"
        }}>
          <p style={{
            fontSize: "17px",
            color: "#86868b",
            textAlign: "center",
            margin: "40px 0"
          }}>
            用户管理功能正在开发中...
          </p>
        </div>
      </div>
    </AdminLayout>
  );
}
