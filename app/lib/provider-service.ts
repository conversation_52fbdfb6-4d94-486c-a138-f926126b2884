import { prisma } from "./prisma";
import { encrypt, decrypt } from "./encryption";
import { ServiceProvider } from "../constant";

export interface ProviderConfig {
  id: string;
  name: string;
  type: string;
  baseUrl: string;
  apiKey: string; // 解密后的API Key
  enabled: boolean;
  config?: any; // 额外配置
  models?: string[]; // 支持的模型列表
  description?: string;
}

export interface CreateProviderData {
  name: string;
  type: string;
  baseUrl: string;
  apiKey: string;
  enabled?: boolean;
  config?: any;
  models?: string[];
  description?: string;
}

/**
 * 获取所有启用的服务商
 */
export async function getEnabledProviders(): Promise<ProviderConfig[]> {
  const providers = await prisma.provider.findMany({
    where: { enabled: true },
    orderBy: { createdAt: "asc" },
  });

  return providers.map((provider) => ({
    id: provider.id,
    name: provider.name,
    type: provider.type,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    enabled: provider.enabled,
    config: provider.config ? JSON.parse(provider.config) : undefined,
    models: provider.models ? JSON.parse(provider.models) : undefined,
    description: provider.description || undefined,
  }));
}

/**
 * 根据类型获取服务商配置
 */
export async function getProviderByType(
  type: string,
): Promise<ProviderConfig | null> {
  const provider = await prisma.provider.findFirst({
    where: {
      type: type,
      enabled: true,
    },
  });

  if (!provider) {
    return null;
  }

  return {
    id: provider.id,
    name: provider.name,
    type: provider.type,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    enabled: provider.enabled,
    config: provider.config ? JSON.parse(provider.config) : undefined,
    models: provider.models ? JSON.parse(provider.models) : undefined,
    description: provider.description || undefined,
  };
}

/**
 * 根据ID获取服务商配置
 */
export async function getProviderById(
  id: string,
): Promise<ProviderConfig | null> {
  const provider = await prisma.provider.findUnique({
    where: { id },
  });

  if (!provider) {
    return null;
  }

  return {
    id: provider.id,
    name: provider.name,
    type: provider.type,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    enabled: provider.enabled,
    config: provider.config ? JSON.parse(provider.config) : undefined,
    models: provider.models ? JSON.parse(provider.models) : undefined,
    description: provider.description || undefined,
  };
}

/**
 * 创建服务商配置
 */
export async function createProvider(
  data: CreateProviderData,
): Promise<ProviderConfig> {
  const encryptedApiKey = encrypt(data.apiKey);

  const provider = await prisma.provider.create({
    data: {
      name: data.name,
      type: data.type,
      baseUrl: data.baseUrl,
      apiKey: encryptedApiKey,
      enabled: data.enabled ?? true,
      config: data.config ? JSON.stringify(data.config) : null,
      models: data.models ? JSON.stringify(data.models) : JSON.stringify([]),
      description: data.description || null,
    },
  });

  return {
    id: provider.id,
    name: provider.name,
    type: provider.type,
    baseUrl: provider.baseUrl,
    apiKey: data.apiKey, // 返回原始API Key
    enabled: provider.enabled,
    config: data.config,
    models: data.models,
    description: data.description,
  };
}

/**
 * 更新服务商配置
 */
export async function updateProvider(
  id: string,
  data: Partial<CreateProviderData>,
): Promise<ProviderConfig | null> {
  const updateData: any = {};

  if (data.name !== undefined) updateData.name = data.name;
  if (data.type !== undefined) updateData.type = data.type;
  if (data.baseUrl !== undefined) updateData.baseUrl = data.baseUrl;
  if (data.apiKey !== undefined) updateData.apiKey = encrypt(data.apiKey);
  if (data.enabled !== undefined) updateData.enabled = data.enabled;
  if (data.config !== undefined)
    updateData.config = data.config ? JSON.stringify(data.config) : null;
  if (data.models !== undefined)
    updateData.models = JSON.stringify(data.models);
  if (data.description !== undefined)
    updateData.description = data.description || null;

  const provider = await prisma.provider.update({
    where: { id },
    data: updateData,
  });

  return {
    id: provider.id,
    name: provider.name,
    type: provider.type,
    baseUrl: provider.baseUrl,
    apiKey: data.apiKey || decrypt(provider.apiKey),
    enabled: provider.enabled,
    config: provider.config ? JSON.parse(provider.config) : undefined,
    models: provider.models ? JSON.parse(provider.models) : undefined,
    description: provider.description || undefined,
  };
}

/**
 * 删除服务商配置
 */
export async function deleteProvider(id: string): Promise<boolean> {
  try {
    await prisma.provider.delete({
      where: { id },
    });
    return true;
  } catch (error) {
    console.error("Delete provider error:", error);
    return false;
  }
}

/**
 * 获取服务商类型映射
 */
export function getProviderTypeMapping(): Record<string, string> {
  return {
    [ServiceProvider.OpenAI]: "openai",
    [ServiceProvider.Azure]: "azure",
    [ServiceProvider.Google]: "google",
    [ServiceProvider.Anthropic]: "anthropic",
    [ServiceProvider.ByteDance]: "bytedance",
    [ServiceProvider.Alibaba]: "alibaba",
    [ServiceProvider.Moonshot]: "moonshot",
    [ServiceProvider.XAI]: "xai",
    [ServiceProvider.DeepSeek]: "deepseek",
    [ServiceProvider.SiliconFlow]: "siliconflow",
  };
}

/**
 * 检查是否启用后端配置
 */
export async function isBackendConfigEnabled(): Promise<boolean> {
  const config = await prisma.systemConfig.findUnique({
    where: { key: "enableBackendConfig" },
  });

  return config?.value === "true";
}

/**
 * 检查是否禁用前端配置
 */
export async function isFrontendConfigDisabled(): Promise<boolean> {
  const config = await prisma.systemConfig.findUnique({
    where: { key: "frontendConfigDisabled" },
  });

  return config?.value === "true";
}
