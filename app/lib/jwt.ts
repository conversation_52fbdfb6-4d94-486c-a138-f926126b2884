import jwt from "jsonwebtoken";
import { NextRequest } from "next/server";

const JWT_SECRET =
  process.env.JWT_SECRET || "default-secret-change-in-production";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

export interface JWTPayload {
  userId: string;
  username: string;
  email: string;
  role: "USER" | "ADMIN";
  iat?: number;
  exp?: number;
}

/**
 * 生成JWT token
 */
export function generateToken(
  payload: Omit<JWTPayload, "iat" | "exp">,
): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    return decoded;
  } catch (error) {
    console.error("JWT verification failed:", error);
    return null;
  }
}

/**
 * 从请求中提取JWT token
 */
export function extractTokenFromRequest(req: NextRequest): string | null {
  // 从 Authorization header 中提取
  const authHeader = req.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // 从 cookie 中提取
  const tokenFromCookie = req.cookies.get("auth-token")?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

/**
 * 验证用户token并返回用户信息
 */
export function verifyUserToken(req: NextRequest): JWTPayload | null {
  const token = extractTokenFromRequest(req);
  if (!token) {
    return null;
  }

  return verifyToken(token);
}

/**
 * 验证管理员权限
 */
export function verifyAdminToken(req: NextRequest): JWTPayload | null {
  const payload = verifyUserToken(req);
  if (!payload || payload.role !== "ADMIN") {
    return null;
  }

  return payload;
}

/**
 * 刷新token
 */
export function refreshToken(oldToken: string): string | null {
  const payload = verifyToken(oldToken);
  if (!payload) {
    return null;
  }

  // 移除旧的时间戳
  const { iat, exp, ...userPayload } = payload;

  return generateToken(userPayload);
}
