import { prisma } from "./prisma";

export interface ApiLogData {
  userId?: string;
  providerId: string;
  model: string;
  endpoint: string;
  method: string;
  tokensUsed?: number;
  cost?: number;
  status: "success" | "error" | "timeout";
  errorMsg?: string;
  duration?: number;
}

export interface ApiLogQuery {
  userId?: string;
  providerId?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

/**
 * 记录API调用日志
 */
export async function logApiCall(data: ApiLogData): Promise<void> {
  try {
    await prisma.apiLog.create({
      data: {
        userId: data.userId || null,
        providerId: data.providerId,
        model: data.model,
        endpoint: data.endpoint,
        method: data.method,
        tokensUsed: data.tokensUsed || null,
        cost: data.cost || null,
        status: data.status,
        errorMsg: data.errorMsg || null,
        duration: data.duration || null,
      },
    });
  } catch (error) {
    console.error("Failed to log API call:", error);
    // 不抛出错误，避免影响主要业务流程
  }
}

/**
 * 查询API调用日志
 */
export async function getApiLogs(query: ApiLogQuery = {}) {
  const where: any = {};

  if (query.userId) where.userId = query.userId;
  if (query.providerId) where.providerId = query.providerId;
  if (query.status) where.status = query.status;
  if (query.startDate || query.endDate) {
    where.createdAt = {};
    if (query.startDate) where.createdAt.gte = query.startDate;
    if (query.endDate) where.createdAt.lte = query.endDate;
  }

  const logs = await prisma.apiLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          username: true,
          email: true,
        },
      },
      provider: {
        select: {
          id: true,
          name: true,
          type: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
    take: query.limit || 100,
    skip: query.offset || 0,
  });

  return logs;
}

/**
 * 获取API调用统计
 */
export async function getApiStats(
  query: Omit<ApiLogQuery, "limit" | "offset"> = {},
) {
  const where: any = {};

  if (query.userId) where.userId = query.userId;
  if (query.providerId) where.providerId = query.providerId;
  if (query.status) where.status = query.status;
  if (query.startDate || query.endDate) {
    where.createdAt = {};
    if (query.startDate) where.createdAt.gte = query.startDate;
    if (query.endDate) where.createdAt.lte = query.endDate;
  }

  const [totalCalls, successCalls, errorCalls, totalTokens, totalCost] =
    await Promise.all([
      // 总调用次数
      prisma.apiLog.count({ where }),

      // 成功调用次数
      prisma.apiLog.count({
        where: { ...where, status: "success" },
      }),

      // 错误调用次数
      prisma.apiLog.count({
        where: { ...where, status: "error" },
      }),

      // 总token使用量
      prisma.apiLog.aggregate({
        where,
        _sum: { tokensUsed: true },
      }),

      // 总成本
      prisma.apiLog.aggregate({
        where,
        _sum: { cost: true },
      }),
    ]);

  return {
    totalCalls,
    successCalls,
    errorCalls,
    successRate:
      totalCalls > 0 ? ((successCalls / totalCalls) * 100).toFixed(2) : "0",
    totalTokens: totalTokens._sum.tokensUsed || 0,
    totalCost: totalCost._sum.cost || 0,
  };
}

/**
 * 获取用户API使用统计
 */
export async function getUserApiStats(userId: string, days: number = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return getApiStats({
    userId,
    startDate,
  });
}

/**
 * 获取服务商API使用统计
 */
export async function getProviderApiStats(
  providerId: string,
  days: number = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return getApiStats({
    providerId,
    startDate,
  });
}

/**
 * 清理过期日志
 */
export async function cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await prisma.apiLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate,
      },
    },
  });

  return result.count;
}
