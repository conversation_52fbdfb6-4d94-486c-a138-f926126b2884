import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import {
  getProviderById,
  updateProvider,
  deleteProvider,
  CreateProviderData,
} from "@/app/lib/provider-service";

interface RouteParams {
  params: {
    id: string;
  };
}

// 获取单个服务商配置
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const provider = await getProviderById(params.id);
    if (!provider) {
      return NextResponse.json({ error: "服务商不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      provider: {
        ...provider,
        apiKey: undefined, // 不返回完整API Key，只返回掩码
        apiKeyMask:
          provider.apiKey.substring(0, 8) + "..." + provider.apiKey.slice(-4),
      },
    });
  } catch (error) {
    console.error("Get provider error:", error);
    return NextResponse.json({ error: "获取服务商配置失败" }, { status: 500 });
  }
}

// 更新服务商配置
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const {
      name,
      type,
      baseUrl,
      apiKey,
      enabled,
      config,
      models,
      description,
    } = body;

    const updateData: Partial<CreateProviderData> = {};

    if (name !== undefined) updateData.name = name;
    if (type !== undefined) updateData.type = type;
    if (baseUrl !== undefined) updateData.baseUrl = baseUrl;
    if (apiKey !== undefined && apiKey !== "") updateData.apiKey = apiKey;
    if (enabled !== undefined) updateData.enabled = enabled;
    if (config !== undefined) updateData.config = config;
    if (models !== undefined) updateData.models = models;
    if (description !== undefined) updateData.description = description;

    const provider = await updateProvider(params.id, updateData);
    if (!provider) {
      return NextResponse.json({ error: "服务商不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      provider: {
        ...provider,
        apiKey: undefined, // 不返回API Key
      },
    });
  } catch (error) {
    console.error("Update provider error:", error);
    return NextResponse.json({ error: "更新服务商配置失败" }, { status: 500 });
  }
}

// 删除服务商配置
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const success = await deleteProvider(params.id);
    if (!success) {
      return NextResponse.json(
        { error: "删除服务商配置失败" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      message: "服务商配置已删除",
    });
  } catch (error) {
    console.error("Delete provider error:", error);
    return NextResponse.json({ error: "删除服务商配置失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
