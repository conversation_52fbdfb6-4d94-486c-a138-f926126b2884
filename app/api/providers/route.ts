import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import {
  getEnabledProviders,
  createProvider,
  updateProvider,
  deleteProvider,
  CreateProviderData,
} from "@/app/lib/provider-service";

// 获取服务商列表
export async function GET(req: NextRequest) {
  try {
    // 管理员可以看到所有服务商，普通用户只能看到启用的
    const tokenData = verifyAdminToken(req);

    if (tokenData) {
      // 管理员：返回所有服务商
      const { prisma } = await import("@/app/lib/prisma");
      const providers = await prisma.provider.findMany({
        orderBy: { createdAt: "asc" },
        select: {
          id: true,
          name: true,
          type: true,
          baseUrl: true,
          enabled: true,
          models: true,
          description: true,
          createdAt: true,
          updatedAt: true,
          // 不返回API Key
        },
      });

      // 解析models字段
      const parsedProviders = providers.map((provider) => ({
        ...provider,
        models: provider.models ? JSON.parse(provider.models) : [],
      }));

      return NextResponse.json({
        success: true,
        providers: parsedProviders,
      });
    } else {
      // 普通用户：只返回启用的服务商基本信息
      const providers = await getEnabledProviders();
      const publicProviders = providers.map((p) => ({
        id: p.id,
        name: p.name,
        type: p.type,
        enabled: p.enabled,
        description: p.description,
        models: p.models,
      }));

      return NextResponse.json({
        success: true,
        providers: publicProviders,
      });
    }
  } catch (error) {
    console.error("Get providers error:", error);
    return NextResponse.json({ error: "获取服务商列表失败" }, { status: 500 });
  }
}

// 创建服务商配置
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const {
      name,
      type,
      baseUrl,
      apiKey,
      enabled,
      config,
      models,
      description,
    } = body;

    // 验证必需字段
    if (!name || !type || !baseUrl || !apiKey) {
      return NextResponse.json(
        { error: "缺少必需字段: name, type, baseUrl, apiKey" },
        { status: 400 },
      );
    }

    const providerData: CreateProviderData = {
      name,
      type,
      baseUrl,
      apiKey,
      enabled: enabled ?? true,
      config,
      models,
      description,
    };

    const provider = await createProvider(providerData);

    return NextResponse.json({
      success: true,
      provider: {
        ...provider,
        apiKey: undefined, // 不返回API Key
      },
    });
  } catch (error) {
    console.error("Create provider error:", error);
    return NextResponse.json({ error: "创建服务商配置失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
