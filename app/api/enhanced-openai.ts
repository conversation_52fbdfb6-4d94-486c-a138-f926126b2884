import { type OpenAIListModelResponse } from "@/app/client/platforms/openai";
import { ModelProvider, OpenaiPath } from "@/app/constant";
import { NextRequest, NextResponse } from "next/server";
import { enhancedAuth } from "./enhanced-auth";
import { enhancedRequest } from "./enhanced-common";

const ALLOWED_PATH = new Set(Object.values(OpenaiPath));

function getModels(
  remoteModelRes: OpenAIListModelResponse,
  backendMode: boolean = false,
) {
  if (backendMode) {
    // 后端模式：可以根据配置过滤模型
    return remoteModelRes;
  } else {
    // 前端模式：不过滤模型，由用户的API密钥权限决定
    return remoteModelRes;
  }
}

export async function enhancedOpenaiHandle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[Enhanced OpenAI Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const subpath = params.path.join("/");

  if (!ALLOWED_PATH.has(subpath)) {
    console.log("[Enhanced OpenAI Route] forbidden path ", subpath);
    return NextResponse.json(
      {
        error: true,
        msg: "you are not allowed to request " + subpath,
      },
      {
        status: 403,
      },
    );
  }

  // 使用增强认证
  const authResult = await enhancedAuth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    // 使用增强请求处理
    const response = await enhancedRequest({
      authResult,
      req,
      providerType: "openai",
      endpoint: subpath,
    });

    // 处理模型列表请求
    if (subpath === OpenaiPath.ListModelPath) {
      const resJson = await response.clone().json();
      const availableModels = getModels(resJson, authResult.backendMode);
      return NextResponse.json(availableModels, {
        status: response.status,
      });
    }

    return response;
  } catch (e) {
    console.error("[Enhanced OpenAI] ", e);
    return NextResponse.json(
      {
        error: true,
        msg: e instanceof Error ? e.message : "Unknown error",
      },
      {
        status: 500,
      },
    );
  }
}

// 向后兼容的处理器
export async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  // 可以选择使用增强版本或保持原有逻辑
  return enhancedOpenaiHandle(req, { params });
}
