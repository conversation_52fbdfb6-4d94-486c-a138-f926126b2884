import { NextRequest, NextResponse } from "next/server";
import { verifyUserToken } from "@/app/lib/jwt";
import {
  getProviderByType,
  isBackendConfigEnabled,
} from "@/app/lib/provider-service";
import { logApiCall } from "@/app/lib/api-log-service";

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  let providerId = "";
  let userId: string | undefined;

  try {
    // 检查是否启用后端配置
    const backendConfigEnabled = await isBackendConfigEnabled();

    if (!backendConfigEnabled) {
      return NextResponse.json(
        { error: "后端配置未启用，请使用原有API接口" },
        { status: 400 },
      );
    }

    // 验证用户身份（可选，支持匿名用户）
    const tokenData = verifyUserToken(req);
    userId = tokenData?.userId;

    // 解析请求体
    const body = await req.json();
    const {
      provider: providerType,
      model,
      messages,
      stream = false,
      ...otherParams
    } = body;

    // 验证必需参数
    if (!providerType || !model || !messages) {
      return NextResponse.json(
        { error: "缺少必需参数: provider, model, messages" },
        { status: 400 },
      );
    }

    // 获取服务商配置
    const providerConfig = await getProviderByType(providerType);
    if (!providerConfig) {
      return NextResponse.json(
        { error: `服务商 ${providerType} 未配置或已禁用` },
        { status: 404 },
      );
    }

    providerId = providerConfig.id;

    // 构建请求URL
    const apiUrl = `${providerConfig.baseUrl}/v1/chat/completions`;

    // 构建请求体
    const requestBody = {
      model,
      messages,
      stream,
      ...otherParams,
      // 合并服务商特定配置
      ...(providerConfig.config || {}),
    };

    // 构建请求头
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${providerConfig.apiKey}`,
    };

    // 添加服务商特定的请求头
    if (providerConfig.config?.headers) {
      Object.assign(headers, providerConfig.config.headers);
    }

    // 发送请求到第三方API
    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    const duration = Date.now() - startTime;

    if (!response.ok) {
      const errorText = await response.text();

      // 记录错误日志
      await logApiCall({
        userId,
        providerId,
        model,
        endpoint: "/v1/chat/completions",
        method: "POST",
        status: "error",
        errorMsg: `HTTP ${response.status}: ${errorText}`,
        duration,
      });

      return NextResponse.json(
        { error: `API调用失败: ${response.status} ${response.statusText}` },
        { status: response.status },
      );
    }

    // 处理流式响应
    if (stream) {
      // 记录成功日志（流式响应无法获取token数量）
      await logApiCall({
        userId,
        providerId,
        model,
        endpoint: "/v1/chat/completions",
        method: "POST",
        status: "success",
        duration,
      });

      // 直接转发流式响应
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      });
    }

    // 处理普通响应
    const responseData = await response.json();

    // 提取token使用量
    const tokensUsed = responseData.usage?.total_tokens;

    // 记录成功日志
    await logApiCall({
      userId,
      providerId,
      model,
      endpoint: "/v1/chat/completions",
      method: "POST",
      tokensUsed,
      status: "success",
      duration,
    });

    return NextResponse.json(responseData);
  } catch (error) {
    const duration = Date.now() - startTime;

    console.error("Chat API error:", error);

    // 记录错误日志
    if (providerId) {
      await logApiCall({
        userId,
        providerId,
        model: "unknown",
        endpoint: "/v1/chat/completions",
        method: "POST",
        status: "error",
        errorMsg: error instanceof Error ? error.message : "Unknown error",
        duration,
      });
    }

    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

export const runtime = "nodejs";
