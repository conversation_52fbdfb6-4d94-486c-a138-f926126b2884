import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";
import { hashPassword } from "@/app/lib/encryption";

interface RouteParams {
  params: {
    id: string;
  };
}

// 获取单个用户信息
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        enabled: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("Get user error:", error);
    return NextResponse.json({ error: "获取用户信息失败" }, { status: 500 });
  }
}

// 更新用户信息
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { username, email, password, role, enabled } = body;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 防止管理员删除自己的管理员权限
    if (existingUser.id === tokenData.userId && role === "USER") {
      return NextResponse.json(
        { error: "不能移除自己的管理员权限" },
        { status: 400 },
      );
    }

    // 构建更新数据
    const updateData: any = {};

    if (username !== undefined) {
      // 检查用户名是否已被其他用户使用
      const userWithSameUsername = await prisma.user.findFirst({
        where: {
          username: username,
          id: { not: params.id },
        },
      });

      if (userWithSameUsername) {
        return NextResponse.json({ error: "用户名已存在" }, { status: 409 });
      }

      updateData.username = username;
    }

    if (email !== undefined) {
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json({ error: "邮箱格式不正确" }, { status: 400 });
      }

      // 检查邮箱是否已被其他用户使用
      const userWithSameEmail = await prisma.user.findFirst({
        where: {
          email: email,
          id: { not: params.id },
        },
      });

      if (userWithSameEmail) {
        return NextResponse.json({ error: "邮箱已被注册" }, { status: 409 });
      }

      updateData.email = email;
    }

    if (password !== undefined && password !== "") {
      // 验证密码强度
      if (password.length < 6) {
        return NextResponse.json({ error: "密码长度至少6位" }, { status: 400 });
      }

      updateData.password = hashPassword(password);
    }

    if (role !== undefined) {
      updateData.role = role;
    }

    if (enabled !== undefined) {
      updateData.enabled = enabled;
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        enabled: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(
      `[Admin] Updated user: ${user.username} by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("Update user error:", error);
    return NextResponse.json({ error: "更新用户信息失败" }, { status: 500 });
  }
}

// 删除用户
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!existingUser) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 防止管理员删除自己
    if (existingUser.id === tokenData.userId) {
      return NextResponse.json(
        { error: "不能删除自己的账户" },
        { status: 400 },
      );
    }

    // 删除用户
    await prisma.user.delete({
      where: { id: params.id },
    });

    console.log(
      `[Admin] Deleted user: ${existingUser.username} by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: "用户已删除",
    });
  } catch (error) {
    console.error("Delete user error:", error);
    return NextResponse.json({ error: "删除用户失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
