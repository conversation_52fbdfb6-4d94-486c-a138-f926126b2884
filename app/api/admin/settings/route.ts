import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

// 获取系统配置
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const category = searchParams.get("category");

    // 构建查询条件
    const where: any = {};
    if (category) {
      where.category = category;
    }

    const settings = await prisma.systemConfig.findMany({
      where,
      orderBy: [{ category: "asc" }, { key: "asc" }],
    });

    // 按分类分组
    const groupedSettings = settings.reduce(
      (acc, setting) => {
        const cat = setting.category || "general";
        if (!acc[cat]) {
          acc[cat] = [];
        }
        acc[cat].push(setting);
        return acc;
      },
      {} as Record<string, typeof settings>,
    );

    return NextResponse.json({
      success: true,
      settings: groupedSettings,
    });
  } catch (error) {
    console.error("Get settings error:", error);
    return NextResponse.json({ error: "获取系统配置失败" }, { status: 500 });
  }
}

// 更新系统配置
export async function PUT(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { settings } = body;

    if (!Array.isArray(settings)) {
      return NextResponse.json({ error: "配置数据格式错误" }, { status: 400 });
    }

    // 批量更新配置
    const updatePromises = settings.map(async (setting: any) => {
      const { key, value, type, description, category } = setting;

      if (!key || value === undefined) {
        throw new Error(`配置项 ${key} 缺少必需字段`);
      }

      // 验证配置值类型
      if (
        type === "boolean" &&
        typeof value !== "boolean" &&
        value !== "true" &&
        value !== "false"
      ) {
        throw new Error(`配置项 ${key} 的值必须是布尔类型`);
      }

      if (type === "number" && isNaN(Number(value))) {
        throw new Error(`配置项 ${key} 的值必须是数字类型`);
      }

      return prisma.systemConfig.upsert({
        where: { key },
        update: {
          value: String(value),
          type: type || "string",
          description: description || null,
          category: category || "general",
          updatedAt: new Date(),
        },
        create: {
          key,
          value: String(value),
          type: type || "string",
          description: description || null,
          category: category || "general",
        },
      });
    });

    await Promise.all(updatePromises);

    console.log(
      `[Admin] Updated ${settings.length} system settings by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: `已更新 ${settings.length} 项配置`,
    });
  } catch (error) {
    console.error("Update settings error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "更新系统配置失败" },
      { status: 500 },
    );
  }
}

// 创建新的系统配置
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const {
      key,
      value,
      type = "string",
      description,
      category = "general",
    } = body;

    // 验证必需字段
    if (!key || value === undefined) {
      return NextResponse.json(
        { error: "缺少必需字段: key, value" },
        { status: 400 },
      );
    }

    // 检查配置是否已存在
    const existingConfig = await prisma.systemConfig.findUnique({
      where: { key },
    });

    if (existingConfig) {
      return NextResponse.json({ error: "配置项已存在" }, { status: 409 });
    }

    // 创建配置
    const config = await prisma.systemConfig.create({
      data: {
        key,
        value: String(value),
        type,
        description: description || null,
        category,
      },
    });

    console.log(
      `[Admin] Created system config: ${key} by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      config,
    });
  } catch (error) {
    console.error("Create setting error:", error);
    return NextResponse.json({ error: "创建系统配置失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
