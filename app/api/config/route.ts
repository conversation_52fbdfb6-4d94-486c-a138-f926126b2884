import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import {
  isBackendConfigEnabled,
  isFrontendConfigDisabled,
  getEnabledProviders,
} from "@/app/lib/provider-service";

// 纯前端应用的默认配置
const DEFAULT_FRONTEND_CONFIG = {
  needCode: false,
  hideUserApiKey: false,
  disableGPT4: false,
  hideBalanceQuery: false,
  disableFastLink: false,
  customModels: "",
  defaultModel: "",
  visionModels: "",
};

// 后端配置模式的配置
const BACKEND_CONFIG = {
  needCode: false,
  hideUserApiKey: true, // 隐藏API Key配置
  disableGPT4: false,
  hideBalanceQuery: true, // 隐藏余额查询
  disableFastLink: false,
  customModels: "",
  defaultModel: "",
  visionModels: "",
  // 后端模式特有配置
  backendMode: true,
  frontendConfigDisabled: true,
};

declare global {
  type DangerConfig = typeof DEFAULT_FRONTEND_CONFIG & {
    backendMode?: boolean;
    frontendConfigDisabled?: boolean;
    providers?: any[];
  };
}

async function handle(req: NextRequest) {
  try {
    // 检查是否启用后端配置
    const backendEnabled = await isBackendConfigEnabled();
    const frontendDisabled = await isFrontendConfigDisabled();

    if (backendEnabled) {
      // 后端配置模式
      const providers = await getEnabledProviders();

      // 获取系统配置
      const systemConfigs = await prisma.systemConfig.findMany({
        where: {
          key: {
            in: [
              "siteName",
              "siteDescription",
              "defaultModel",
              "customModels",
              "visionModels",
            ],
          },
        },
      });

      const configMap = systemConfigs.reduce(
        (acc, config) => {
          acc[config.key] = config.value;
          return acc;
        },
        {} as Record<string, string>,
      );

      const config = {
        ...BACKEND_CONFIG,
        frontendConfigDisabled: frontendDisabled,
        defaultModel: configMap.defaultModel || "",
        customModels: configMap.customModels || "",
        visionModels: configMap.visionModels || "",
        providers: providers.map((p) => ({
          id: p.id,
          name: p.name,
          type: p.type,
          enabled: p.enabled,
          models: p.models,
        })),
      };

      return NextResponse.json(config);
    } else {
      // 前端配置模式（原有模式）
      return NextResponse.json({
        ...DEFAULT_FRONTEND_CONFIG,
        backendMode: false,
        frontendConfigDisabled: false,
      });
    }
  } catch (error) {
    console.error("Config API error:", error);
    // 出错时返回前端配置模式
    return NextResponse.json({
      ...DEFAULT_FRONTEND_CONFIG,
      backendMode: false,
      frontendConfigDisabled: false,
    });
  }
}

export const GET = handle;
export const POST = handle;

export const runtime = "nodejs";
