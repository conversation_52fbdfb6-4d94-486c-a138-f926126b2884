import { ApiPath } from "@/app/constant";
import { NextRequest } from "next/server";
import { handle as openaiHandler } from "../../openai";
import { enhancedOpenaiHandle } from "../../enhanced-openai";
import { handle as azureHandler } from "../../azure";
import { handle as googleHandler } from "../../google";
import { handle as anthropicHandler } from "../../anthropic";
import { handle as bytedanceHandler } from "../../bytedance";
import { handle as alibabaHandler } from "../../alibaba";
import { handle as moonshotHandler } from "../../moonshot";
import { handle as deepseekHandler } from "../../deepseek";
import { handle as siliconflowHandler } from "../../siliconflow";
import { handle as xaiHandler } from "../../xai";
import { handle as proxyHandler } from "../../proxy";
import { handle as customProviderHandler } from "../../custom-provider";
import { isBackendConfigEnabled } from "@/app/lib/provider-service";

async function handle(
  req: NextRequest,
  { params }: { params: { provider: string; path: string[] } },
) {
  const apiPath = `/api/${params.provider}`;
  console.log(`[${params.provider} Route] params `, params);

  // 检查是否是自定义服务商（以custom_开头）
  if (params.provider.startsWith("custom_")) {
    return customProviderHandler(req, { params });
  }

  // 检查是否启用后端配置，决定使用增强版还是原版处理器
  try {
    const backendEnabled = await isBackendConfigEnabled();

    if (backendEnabled) {
      // 后端模式：所有服务商都使用增强版处理器
      const { enhancedProviderHandle } = await import(
        "../../enhanced-provider"
      );
      return enhancedProviderHandle(req, { params });
    } else {
      // 前端模式：使用原版处理器
      switch (apiPath) {
        case ApiPath.Azure:
          return azureHandler(req, { params });
        case ApiPath.Google:
          return googleHandler(req, { params });
        case ApiPath.Anthropic:
          return anthropicHandler(req, { params });
        case ApiPath.ByteDance:
          return bytedanceHandler(req, { params });
        case ApiPath.Alibaba:
          return alibabaHandler(req, { params });
        case ApiPath.Moonshot:
          return moonshotHandler(req, { params });
        case ApiPath.DeepSeek:
          return deepseekHandler(req, { params });
        case ApiPath.XAI:
          return xaiHandler(req, { params });
        case ApiPath.SiliconFlow:
          return siliconflowHandler(req, { params });
        case ApiPath.OpenAI:
          return openaiHandler(req, { params });
        default:
          return proxyHandler(req, { params });
      }
    }
  } catch (error) {
    console.error("[Route] Error checking backend config:", error);
    // 出错时回退到原有处理器
    switch (apiPath) {
      case ApiPath.Azure:
        return azureHandler(req, { params });
      case ApiPath.Google:
        return googleHandler(req, { params });
      case ApiPath.Anthropic:
        return anthropicHandler(req, { params });
      case ApiPath.ByteDance:
        return bytedanceHandler(req, { params });
      case ApiPath.Alibaba:
        return alibabaHandler(req, { params });
      case ApiPath.Moonshot:
        return moonshotHandler(req, { params });
      case ApiPath.DeepSeek:
        return deepseekHandler(req, { params });
      case ApiPath.XAI:
        return xaiHandler(req, { params });
      case ApiPath.SiliconFlow:
        return siliconflowHandler(req, { params });
      case ApiPath.OpenAI:
        return openaiHandler(req, { params });
      default:
        return proxyHandler(req, { params });
    }
  }
}

export const GET = handle;
export const POST = handle;

// 使用 Node.js runtime 以支持数据库和JWT操作
export const runtime = "nodejs";
export const preferredRegion = [
  "arn1",
  "bom1",
  "cdg1",
  "cle1",
  "cpt1",
  "dub1",
  "fra1",
  "gru1",
  "hnd1",
  "iad1",
  "icn1",
  "kix1",
  "lhr1",
  "pdx1",
  "sfo1",
  "sin1",
  "syd1",
];
