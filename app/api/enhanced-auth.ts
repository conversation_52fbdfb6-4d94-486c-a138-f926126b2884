import { NextRequest } from "next/server";
import { ACCESS_CODE_PREFIX, ModelProvider } from "../constant";
import { verifyUserToken } from "../lib/jwt";
import {
  isBackendConfigEnabled,
  isFrontendConfigDisabled,
  getProviderByType,
} from "../lib/provider-service";

function getIP(req: NextRequest) {
  let ip = req.ip ?? req.headers.get("x-real-ip");
  const forwardedFor = req.headers.get("x-forwarded-for");

  if (!ip && forwardedFor) {
    ip = forwardedFor.split(",").at(0) ?? "";
  }

  return ip;
}

function parseApiKey(bearToken: string) {
  const token = bearToken.trim().replaceAll("Bearer ", "").trim();
  const isApiKey = !token.startsWith(ACCESS_CODE_PREFIX);

  return {
    accessCode: isApiKey ? "" : token.slice(ACCESS_CODE_PREFIX.length),
    apiKey: isApiKey ? token : "",
  };
}

export interface AuthResult {
  error: boolean;
  msg?: string;
  userId?: string;
  apiKey?: string;
  baseUrl?: string;
  providerId?: string;
  backendMode?: boolean;
}

/**
 * 增强的认证函数，支持前端和后端两种模式
 */
export async function enhancedAuth(
  req: NextRequest,
  modelProvider: ModelProvider,
): Promise<AuthResult> {
  const authToken = req.headers.get("Authorization") ?? "";
  const { accessCode, apiKey } = parseApiKey(authToken);

  console.log("[User IP] ", getIP(req));
  console.log("[Time] ", new Date().toLocaleString());

  try {
    // 检查是否启用后端配置
    const backendEnabled = await isBackendConfigEnabled();
    const frontendDisabled = await isFrontendConfigDisabled();

    if (backendEnabled) {
      console.log("[Auth] Backend config mode enabled");

      // 后端配置模式：验证用户身份并使用后端配置的API Key
      const tokenData = verifyUserToken(req);

      // 在后端模式下，允许匿名用户使用（可选）
      // 如果需要强制登录，可以取消注释下面的代码
      // if (!tokenData) {
      //   return {
      //     error: true,
      //     msg: "需要登录才能使用此服务",
      //   };
      // }

      // 获取服务商配置
      const providerTypeMap: Record<ModelProvider, string> = {
        [ModelProvider.GPT]: "openai",
        [ModelProvider.GeminiPro]: "google",
        [ModelProvider.Claude]: "anthropic",
        [ModelProvider.Doubao]: "bytedance",
        [ModelProvider.Qwen]: "alibaba",
        [ModelProvider.Moonshot]: "moonshot",
        [ModelProvider.XAI]: "xai",
        [ModelProvider.DeepSeek]: "deepseek",
        [ModelProvider.SiliconFlow]: "siliconflow",
      };

      const providerType = providerTypeMap[modelProvider];
      if (!providerType) {
        return {
          error: true,
          msg: `不支持的服务商类型: ${modelProvider}`,
        };
      }

      const providerConfig = await getProviderByType(providerType);
      if (!providerConfig) {
        return {
          error: true,
          msg: `服务商 ${providerType} 未配置或已禁用`,
        };
      }

      console.log("[Auth] Using backend provider config:", providerConfig.name);

      return {
        error: false,
        userId: tokenData?.userId,
        apiKey: providerConfig.apiKey,
        baseUrl: providerConfig.baseUrl,
        providerId: providerConfig.id,
        backendMode: true,
      };
    } else {
      console.log("[Auth] Frontend config mode (legacy)");

      // 前端配置模式（原有逻辑）
      if (!apiKey) {
        return {
          error: true,
          msg: "API key is required for this frontend-only application",
        };
      }

      console.log("[Auth] Using user provided API key");

      return {
        error: false,
        apiKey,
        backendMode: false,
      };
    }
  } catch (error) {
    console.error("[Auth] Error:", error);

    // 出错时回退到前端模式
    if (!apiKey) {
      return {
        error: true,
        msg: "Authentication failed, API key required",
      };
    }

    return {
      error: false,
      apiKey,
      backendMode: false,
    };
  }
}

/**
 * 向后兼容的认证函数
 */
export function auth(req: NextRequest, modelProvider: ModelProvider) {
  // 为了向后兼容，这里返回同步结果
  // 新的API应该使用 enhancedAuth
  const authToken = req.headers.get("Authorization") ?? "";
  const { accessCode, apiKey } = parseApiKey(authToken);

  console.log("[User IP] ", getIP(req));
  console.log("[Time] ", new Date().toLocaleString());

  if (!apiKey) {
    return {
      error: true,
      msg: "API key is required for this frontend-only application",
    };
  }

  console.log("[Auth] use user api key (legacy mode)");

  return {
    error: false,
  };
}
