import { NextRequest, NextResponse } from "next/server";
import { <PERSON><PERSON><PERSON><PERSON>, ModelProvider } from "../constant";
import { enhancedAuth } from "./enhanced-auth";
import { enhancedRequest } from "./enhanced-common";

// 服务商类型映射
const PROVIDER_MAP: Record<string, ModelProvider> = {
  openai: ModelProvider.GPT,
  google: ModelProvider.GeminiPro,
  anthropic: ModelProvider.Claude,
  bytedance: ModelProvider.Doubao,
  alibaba: ModelProvider.Qwen,
  moonshot: ModelProvider.Moonshot,
  xai: ModelProvider.XAI,
  deepseek: ModelProvider.DeepSeek,
  siliconflow: ModelProvider.SiliconFlow,
};

// 允许的路径映射
const ALLOWED_PATHS: Record<string, Set<string>> = {
  openai: new Set([
    "v1/chat/completions",
    "v1/completions",
    "v1/models",
    "v1/audio/speech",
    "v1/images/generations",
    "dashboard/billing/usage",
    "dashboard/billing/subscription",
  ]),
  google: new Set([
    "v1beta/models",
    "v1beta/models/gemini-pro:streamGenerateContent",
    "v1beta/models/gemini-pro-vision:streamGenerateContent",
    "v1beta/models/gemini-1.5-pro:streamGenerateContent",
    "v1beta/models/gemini-1.5-flash:streamGenerateContent",
  ]),
  anthropic: new Set(["v1/messages", "v1/complete"]),
  bytedance: new Set(["api/v3/chat/completions", "api/v1/models"]),
  alibaba: new Set([
    "v1/services/aigc/text-generation/generation",
    "v1/services/aigc/multimodal-generation/generation",
    "v1/models",
  ]),
  moonshot: new Set(["v1/chat/completions", "v1/models"]),
  xai: new Set(["v1/chat/completions", "v1/models"]),
  deepseek: new Set(["v1/chat/completions", "v1/models"]),
  siliconflow: new Set(["v1/chat/completions", "v1/models"]),
};

/**
 * 增强版服务商处理器
 * 支持所有服务商的后端配置模式
 */
export async function enhancedProviderHandle(
  req: NextRequest,
  { params }: { params: { provider: string; path: string[] } },
) {
  console.log(`[Enhanced ${params.provider}] params:`, params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const providerType = params.provider;
  const subpath = params.path.join("/");

  // 检查服务商类型是否支持
  const modelProvider = PROVIDER_MAP[providerType];
  if (!modelProvider) {
    console.log(`[Enhanced Provider] Unsupported provider: ${providerType}`);
    return NextResponse.json(
      {
        error: true,
        msg: `Unsupported provider: ${providerType}`,
      },
      { status: 400 },
    );
  }

  // 检查路径是否允许
  const allowedPaths = ALLOWED_PATHS[providerType];
  if (allowedPaths && !allowedPaths.has(subpath)) {
    console.log(`[Enhanced ${providerType}] Forbidden path: ${subpath}`);
    return NextResponse.json(
      {
        error: true,
        msg: `You are not allowed to request ${subpath}`,
      },
      { status: 403 },
    );
  }

  // 使用增强认证
  const authResult = await enhancedAuth(req, modelProvider);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    // 使用增强请求处理
    const response = await enhancedRequest({
      authResult,
      req,
      providerType,
      endpoint: subpath,
    });

    // 特殊处理模型列表请求
    if (subpath.includes("models") || subpath.includes("v1/models")) {
      try {
        const resJson = await response.clone().json();

        // 根据服务商类型过滤模型（如果需要）
        if (authResult.backendMode) {
          // 在后端模式下，可以根据配置过滤模型
          // 这里暂时返回原始结果，后续可以根据需要添加过滤逻辑
        }

        return NextResponse.json(resJson, {
          status: response.status,
          headers: response.headers,
        });
      } catch (e) {
        // 如果解析JSON失败，返回原始响应
        return response;
      }
    }

    return response;
  } catch (e) {
    console.error(`[Enhanced ${providerType}]`, e);
    return NextResponse.json(
      {
        error: true,
        msg: e instanceof Error ? e.message : "Unknown error",
      },
      {
        status: 500,
      },
    );
  }
}

/**
 * 向后兼容的处理器
 */
export async function handle(
  req: NextRequest,
  { params }: { params: { provider: string; path: string[] } },
) {
  return enhancedProviderHandle(req, { params });
}
