import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { hashPassword } from "@/app/lib/encryption";
import { generateToken } from "@/app/lib/jwt";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { username, email, password } = body;

    // 验证输入
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: "用户名、邮箱和密码不能为空" },
        { status: 400 },
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "邮箱格式不正确" }, { status: 400 });
    }

    // 验证密码强度
    if (password.length < 6) {
      return NextResponse.json({ error: "密码长度至少6位" }, { status: 400 });
    }

    // 检查是否允许注册
    const allowRegistration = await prisma.systemConfig.findUnique({
      where: { key: "allowUserRegistration" },
    });

    if (allowRegistration?.value === "false") {
      return NextResponse.json(
        { error: "当前不允许用户注册" },
        { status: 403 },
      );
    }

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ username: username }, { email: email }],
      },
    });

    if (existingUser) {
      if (existingUser.username === username) {
        return NextResponse.json({ error: "用户名已存在" }, { status: 409 });
      } else {
        return NextResponse.json({ error: "邮箱已被注册" }, { status: 409 });
      }
    }

    // 获取默认用户角色
    const defaultRole = await prisma.systemConfig.findUnique({
      where: { key: "defaultUserRole" },
    });

    // 创建用户
    const hashedPassword = hashPassword(password);
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        role: (defaultRole?.value as "USER" | "ADMIN") || "USER",
        enabled: true,
      },
    });

    // 生成JWT token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role as "USER" | "ADMIN",
    });

    // 返回用户信息和token
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
      },
      token,
    });

    // 设置cookie
    response.cookies.set("auth-token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "注册失败，请稍后重试" },
      { status: 500 },
    );
  }
}

export const runtime = "nodejs";
