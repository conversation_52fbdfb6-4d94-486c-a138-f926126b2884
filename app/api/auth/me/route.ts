import { NextRequest, NextResponse } from "next/server";
import { verifyUserToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    // 验证token
    const tokenData = verifyUserToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "未授权访问" }, { status: 401 });
    }

    // 从数据库获取最新用户信息
    const user = await prisma.user.findUnique({
      where: {
        id: tokenData.userId,
        enabled: true,
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        enabled: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "用户不存在或已被禁用" },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("Get user info error:", error);
    return NextResponse.json({ error: "获取用户信息失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
